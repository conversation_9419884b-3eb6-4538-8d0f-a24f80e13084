/**********************************************************************
* NXEcProAsdu.h         author:jjl      date:23/10/2013            
*---------------------------------------------------------------------
*  note:IEC103/104ASDU处理结点类头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXECPROASDU_H_ 
#define _H_NXECPROASDU_H_

#include "ec_pro_common_def.h"
#include "INXEcSSModelSeek.h"
#include "TimeConvert.h"
#include "EcTemplateFunc.h"
#include "EcProCommonFun.h"

////////////////////////////////////////////////////////////// 类型定义
/** @brief         无数据*/
const int GDD_DATA_TYPE_NO      = 0;
/** @brief         ASCII*/
const int GDD_DATA_TYPE_ASCII   = 1;
/** @brief         位串*/
const int GDD_DATA_TYPE_BS1     = 2;
/** @brief         无符号整数*/
const int GDD_DATA_TYPE_UI      = 3;
/** @brief         整数*/
const int GDD_DATA_TYPE_I       = 4;
/** @brief         无符号浮点*/
const int GDD_DATA_TYPE_UF      = 5;
/** @brief         浮点数*/
const int GDD_DATA_TYPE_F       = 6;
/** @brief         R32.23 短实数*/
const int GDD_DATA_TYPE_R32     = 7;
/** @brief         R64.53 实数*/
const int GDD_DATA_TYPE_R64     = 8;
/** @brief         双点信息*/
const int GDD_DATA_TYPE_DPI     = 9;
/** @brief         单点信息*/
const int GDD_DATA_TYPE_SPI     = 10;
/** @brief         带顺便和差错的双点信息*/
const int GDD_DATA_TYPE_WITH_TRAN_ERR = 11;
/** @brief         二进制时间*/
const int GDD_DATA_TYPE_CP56TM  = 14;
/** @brief         带4字节时标的双点信息*/
const int GDD_DATA_TYPE_DPI_CP48= 18;
/** @brief         带7字节及相对时间的双点信息*/
const int GDD_DATA_TYPE_DPI_CP80= 19;
/** @brief         通用分类回答码*/
const int GDD_DATA_TYPE_GRC     = 22;
/** @brief         数据结构*/
const int GDD_DATA_TYPE_DATASTRUCT= 23;

///////////////////////////////////////////////////////////////结构定义
/** @brief    字符串处理结构体,包括字符串指针和需要处理的长度*/
typedef struct _CHAR_HANDLE
{
	char * mypchar;
	int    charlenght;
	_CHAR_HANDLE()
	{
		mypchar   =  NULL;
		charlenght = 0;
	}
}CHAR_HANDLE;

/** @brief         录波文件、通用文件列表信息*/
typedef list<FILE_PROPERTY_INF> ASDU_FILE_LIST;

/** @brief         信息体标识符*/
typedef struct _ASDU_INFO_OBJ
{
	/** @brief         功能类型*/
	u_int8 nFun;
	/** @brief         信息序号*/
	u_int8 nInf;
	/** @brief         DPI值*/
	u_int8 nDpi;

	_ASDU_INFO_OBJ()
	{
		nFun = 0;
		nInf = 0;
		nDpi = 0;
	}
}ASDU_INFO_OBJ;

/** @brief         信息体标识对象列表*/
typedef list<ASDU_INFO_OBJ> ASDU_INFO_OBJ_LIST;

/** @brief         地址信息*/
typedef struct _ASDU_ADDR
{
	/** @brief         厂站地址*/
	int16 nSubstationAdd;
	/** @brief         设备地址*/
	int16 nAddr;
	/** @brief         设备cpu号*/
	int8 nCpu;
	/** @brief         设备定值区号*/
	int8 nZone;

	_ASDU_ADDR()
	{
		nSubstationAdd = 0;
		nAddr          = 0;
		nCpu           = 0;
		nZone          = 0;
	}
}ASDU_ADDR;

/** @brief         时间信息*/
typedef struct _ASDU_TIME
{
	/** @brief         信息发生时间(UTC秒)*/
	time_t nInfoHappenUtc;
	/** @brief         信息发生时间毫秒*/
	int16 nInfoHappenMs;
	/** @brief         相对时间(信息发生相对于设备启动)*/
	int16  nRetTime;	
	/** @brief         信息接收时间(UTC秒)*/
	time_t nInfoRcvUtc;
	/** @brief         信息接收时间毫秒*/
	int16 nInfoRcvMs;

	_ASDU_TIME()
	{
		nInfoHappenUtc = 0;
		nInfoHappenMs  = 0;
		nRetTime       = 0;
		nInfoRcvUtc    = 0;
		nInfoRcvMs     = 0;
	}

}ASDU_TIME;

/** @brief         通用分类信息*/
typedef struct _ASDU_GEN_INFO
{
	/** @brief         组号*/
	u_int8 nGroup;
	/** @brief         条目号*/
	u_int8 nEntry;
	/** @brief         描述类别*/
	u_int8 nKod;
    /** @brief         数据类型*/
	u_int8 nDataType;
	/** @brief         数据宽度*/
	u_int8 nDataSize;
	/** @brief         数据数目*/
	u_int8 nDataNumber;
	/** @brief         通用分类标识数据*/
	PRO_FRAME_DATA vGid;

	_ASDU_GEN_INFO()
	{
		nGroup    = 0;
		nEntry    = 0;
		nKod      = 0;
		nDataType = 0;
		nDataSize = 0;
		nDataNumber=0;
	}
}ASDU_GEN_INFO;

/** @brief         通用分类信息列表*/
typedef list<ASDU_GEN_INFO> ASDU_GEN_INFO_LIST;

/** @brief         通用分类组号与其数据集映射表*/
typedef map<u_int8,ASDU_GEN_INFO_LIST*> GROUP2GENLIST_MAP;

/** @brief         通用分类组号与返回信息标识符映射表*/
typedef map<u_int8,u_int8> GROUP2RII_MAP;

/** @brief         故障分析类信息*/
typedef struct _ASDU_FAULT_INFO
{
	/** @brief         故障序号*/
	u_int16 nFan;
	/** @brief         故障类型*/
	int8  nFpt;
	/** @brief         跳闸类型*/
	int8  nJpt;
	/** @brief         测距结果*/
	float  fScl;
	/** @brief         重合闸时间(毫秒)*/
	int16  nRecloseMs;
	/** @brief         录波文件名*/
	string strWavFileName;
	/** @brief         间隔名称*/
	string strBayName;
	/** @brief         备用字符*/
	string strReserve;
	/** @brief         整形备用*/
	int    nReserve;
	_ASDU_FAULT_INFO()
	{
		nFan      = 0;
		nFpt      = 0;
		nJpt      = 0;
		fScl      = 0;
		nRecloseMs= 0;
		nReserve  = 0;
	}
}ASDU_FAULT_INFO;

/** @brief         事件类型枚举*/
typedef enum _ENUM_EVENT_TYPE
{
	EVENT_ACTION = 1, /**<      动作 */
	EVENT_ALARM  = 2, /**<      告警 */
	EVENT_HARDSTRAP=3,/**<      硬压板*/
	EVENT_DISTANCE =4,/**<      测距*/
	EVENT_FAULT    =5,/**<      故障量*/
	EVENT_SOFTSTRAP=6,/**<      软压板*/
	EVENT_ALL      =255,/**<    所有信息*/

}ENUM_EVENT_TYPE;

/** @brief         历史事件信息*/
typedef struct _ASDU_HIS_INFO
{
	/** @brief         事件类型*/
	ENUM_EVENT_TYPE    eType;
	/** @brief         地址信息*/
	ASDU_ADDR          Addr;
	/** @brief         信息体*/
	ASDU_INFO_OBJ      InfoObj;
	/** @brief         时间*/
	ASDU_TIME          InfoTime;
	/** @brief         故障类信息*/
	ASDU_FAULT_INFO    FaultInfo;
	/** @brief         通用分类信息*/
	ASDU_GEN_INFO_LIST GenList;

}ASDU_HIS_INFO;

/** @brief         历史事件信息列表*/
typedef list<ASDU_HIS_INFO> ASDU_HIS_INFO_LIST;

/** @brief         ASDU2、4、12信息*/
typedef struct _ASDU2_4_12_INFO
{
	/** @brief         地址信息*/
	ASDU_ADDR          Addr;
	/** @brief         信息体*/
	ASDU_INFO_OBJ      InfoObj;
	/** @brief         时间*/
	ASDU_TIME          InfoTime;
	/** @brief         故障类信息*/
	ASDU_FAULT_INFO    FaultInfo;
	/** @brief         品质*/
	u_int8             nQuality;
	/** @brief         附加信息*/
	u_int8             nSin;
	/** @brief         备用*/
	int                nReserve;

	_ASDU2_4_12_INFO()
	{
		nQuality = 1;
		nSin = 0;
		nReserve = 0;
	}
}ASDU2_4_12_INFO;

/** @brief         ASDU2\4\12列表*/
typedef list<ASDU2_4_12_INFO> ASDU2_4_12_INFO_LIST;

/** @brief         ASDU10信息*/
typedef struct _ASDU10_INFO
{
	/** @brief         传输原因*/
	u_int8             nCot;
	/** @brief         地址信息*/
	ASDU_ADDR          Addr;
	/** @brief         信息体*/
	ASDU_INFO_OBJ      InfoObj;
	/** @brief         通用分类组号与其数据集信息映射表*/
	GROUP2GENLIST_MAP  GroupToGenListMap;
	/** @brief         时间*/
	ASDU_TIME          InfoTime;
	/** @brief         品质*/
	u_int8             nQuality;
	/** @brief         组号*/
	int16              nGroup;
	/** @brief         备用*/
	int                nReserve1;
	/** @brief         备用*/
	int                nReserve2;
	_ASDU10_INFO()
	{
		nCot     = 0;
		nQuality = 1;
		nGroup   = -1;
		nReserve1 = 0;
		nReserve2 = 0;
	}

}ASDU10_INFO;

/** @brief         ASDU10信息列表*/
typedef list<ASDU10_INFO> ASDU10_INFO_LIST;

/** @brief         */

/**
* @defgroup    TNXEcProAsdu :ASDU处理节点类
* @{
*/
 
/**
 * @brief      各ASDU的公用实现及接口定义
 * <AUTHOR>
 * @date       23/10/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class TNXEcProAsdu:public CNXECObject
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~TNXEcProAsdu();

protected:
    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

	///////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         转换规约信息到NX事件消息结构
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    NX_EVENT_MSG_LIST & lMsg :转换生成的事件消息列表
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertProToEventMsg(IN  PRO_FRAME_BODY * pBody,OUT NX_EVENT_MSG_LIST & lMsg) ;

	/**
	* @brief         转换规约信息到NX通用消息结构
	* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
	* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
	* @return        >=0:成功 <0:失败
	*/
	virtual int ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         直接从本地生成结果回应，如初始化配置信息;
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult) ;

	/**
	* @brief         根据NX事件信息生成规约事件列表
	* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
	* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody) ;

	/**
	* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
	* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
	* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief         设置规约的时间格式
	* @param[in]     bool bCP56Time:是否为CP56格式,默认是
	* @return        void
	*/
	virtual void     SetProTimeFormat(bool bCP56Time = true);

	/**
	* @brief         设置规约是否带本地接收时间
	* @param[in]     bool bHaveRcvTime:是否带本地接收时间,默认否
	* @return        void
	*/
	virtual void     SetIsHaveRcvTime(bool bHaveRcvTime = false);

	/**
	* @brief         设置规约的自动上送事件传输原因是否受品质因素影响，即是否可直接标识并上传检修类信息
	* @param[in]     bool bCotByQuality:是否需根据品质决定传输原因,默认否
	* @return         void
	*/
	virtual void     SetCotSupportTestInfo(bool bCotByQuality = false);

	/**
	* @brief         设置规约上传故障量的形式
	* @param[in]     bool bGeneric:true-通用分类 false-专用报文,默认专用报告
	* @return        void
	*/
	virtual void     SetFaultParamFormat(bool bGeneric = false);

	/**
	* @brief         设置规约报文体最大长度
	* @param[in]     int nAsduMaxLen:报文体最大长度,默认:总长(2048)-报文头(7) = 2041;
	* @return        void
	*/
	void            SetAsduMaxLen(IN int nAsduMaxLen = 2041);

	/**
	* @brief         设置不订阅设备列表(从本地获取初始化配置时使用)
	* @param[in]     LIST_NOT_ORDER * pNotOrderDevList:不订阅设备列表
	* @param[out]    无
	* @return        void
	*/
	void           SetNotOrderDevList( IN LIST_NOT_ORDER * pNotOrderDevList);

	/**
	* @brief		传入csg日志指针.			
	* @param[in]    CCsgLogRecordMngr * pCsgLog: csg日志指针.
	**/
	void SetCsgLogObj(CCsgLogRecordMngr * pCsgLog);
	////////////////////////////////////////////////////////////////////////保护方法
protected:

	/**
	* @brief         获得ASDU可变部分最大长度
	* @param[in]     无
	* @return        int
	*/
	virtual int     GetAsduVarDataMaxLen();

	/**
	* @brief         获取不订阅设备列表配置(从本地获取初始化配置时使用)
	* @param[in]     LIST_NOT_ORDER * pNotOrderDevList:不订阅设备列表
	* @param[out]    无
	* @return        const LIST_NOT_ORDER * :不订阅设备列表配置指针
	*/
	const LIST_NOT_ORDER * GetNotOrderDevList();

	/**
	* @brief         清空ASdu10信息结构内容
	* @param[in]     ASDU10_INFO & Asdu10Info:结构引用
	* @return        void
	*/
	virtual void ClearAsdu10Info(IN ASDU10_INFO & Asdu10Info);
	
	///////////////////////////////////////////////////////////////////////可能需要重载的方法
protected:

	/**
	* @brief         根据信息品质获得突发信息传输原因
	* @param[in]     无
	* @return        u_int8 :传输原因
	*/
	virtual u_int8 __GetAutoSendCot(u_int8 nQuality);

	/**
	* @brief         获得组标题类型
	* @param[in]     u_int16 nSubStationAddr:变电站地址
	* @param[in]     u_int16 nDevAddr:装置地址
	* @param[in]     u_int16 nLD:CPU
	* @param[in]     u_int16 nGroup:组号
	* @return        int :组标题类型
	*/
	virtual int _GetGroupTitleType(u_int16 nSubStationAddr,u_int16 nDevAddr,u_int16 nLd,u_int16 nGroup);

	/**
	* @brief         根据召唤方式获得kod
	* @param[in]     int nCallWay:召唤方式-装置或数据库
	* @return        void
	*/
	virtual u_int8 ___GetGenKod(IN int nCallWay);

	/**
	* @brief         设置浮点型数据的通用分类数据
	* @param[in]     nGroup:组号
	* @param[in]     nEntry:条目号
	* @param[in]     nKod:描述符
	* @param[in]     fValue:数据值
	* @param[out]    ASDU_GEN_INFO &GenData:通用分类数据
	* @return        void
	*/
	virtual void __SetGenericFloatData(IN u_int8 nGroup,IN u_int8 nEntry,IN u_int8 nKod,IN float fValue,OUT ASDU_GEN_INFO &GenData);

	/**
	* @brief         设置整型数据的通用分类数据
	* @param[in]     nGroup:组号
	* @param[in]     nEntry:条目号
	* @param[in]     nKod:描述符
	* @param[in]     nValue:数据值
	* @param[out]    ASDU_GEN_INFO &GenData:通用分类数据
	* @param[in]     bDpi:数据类型是否为双点
	* @return        void
	*/
	virtual void __SetGenericIntData(IN u_int8 nGroup,IN u_int8 nEntry,IN u_int8 nKod,IN int nValue,OUT ASDU_GEN_INFO &GenData,IN bool bDpi = false);

	/**
	* @brief         设置字符型数据的通用分类数据
	* @param[in]     nGroup:组号
	* @param[in]     nEntry:条目号
	* @param[in]     nKod:描述符
	* @param[in]     cValue:数据值
	* @param[out]    ASDU_GEN_INFO &GenData:通用分类数据
	* @return        void
	*/
	virtual void __SetGenericStrData(IN u_int8 nGroup,IN u_int8 nEntry,IN u_int8 nKod,IN char* cValue,OUT ASDU_GEN_INFO &GenData);

	/**
	* @brief         设置时间型数据的通用分类数据
	* @param[in]     nGroup:组号
	* @param[in]     nEntry:条目号
	* @param[in]     nKod:描述符
	* @param[in]     strValue:CP56或CP32格式时间
	* @param[out]    ASDU_GEN_INFO &GenData:通用分类数据
	* @return        void
	*/
	virtual void __SetGenericTimeData(IN u_int8 nGroup,IN u_int8 nEntry,IN u_int8 nKod,IN string &strValue,OUT ASDU_GEN_INFO &GenData);

	/**
	* @brief         设置数据结构类型的通用分类数据
	* @param[in]     nGroup:组号
	* @param[in]     nEntry:条目号
	* @param[in]     nKod:描述符
	* @param[in]     PRO_FRAME_DATA &vDataStruct
	* @param[out]    ASDU_GEN_INFO &GenData:通用分类数据
	* @return        void
	*/
	virtual void __SetGenericStructData(IN u_int8 nGroup,IN u_int8 nEntry,IN u_int8 nKod,IN PRO_FRAME_DATA &vDataStruct,OUT ASDU_GEN_INFO &GenData);

	/**
	* @brief         将通用分类各种数据类型的GID统一转换为string类型
	* @param[in]     ASDU_GEN_INFO & GenInfo:通用分类数据点信息
	* @return        string:字符串类型值
	*/
	virtual string ___CvtGenericValueToStr(IN ASDU_GEN_INFO & GenInfo);

	/**
	* @brief      增加一个整形数据属性到数据结构中
	* @param[in]  int nValue:数据值
	* @param[out] PRO_FRAME_DATA &vData: 保存数据结构信息
	* @param[in]  int nDataSize:数据宽度
	* @param[in]  bool bDpi: 标识是否为DPI,默认否
	* @return     void
	*/
	virtual void __AddIntDataToStruct(IN int nValue,IN int nDataSize,OUT PRO_FRAME_DATA &vData,IN bool bDpi = false);

	/**
	* @brief      增加一个浮点型数据属性到数据结构中
	* @param[in]  float fValue:数据值
	* @param[out] PRO_FRAME_DATA &vData: 保存数据结构信息
	* @return     void
	*/
	virtual void __AddFloatDataToStruct(IN float fValue,OUT PRO_FRAME_DATA &vData);

	/**
	* @brief      增加其它类型数据属性到数据结构中
	* @param[in]  int nDataType:数据类型
	* @param[in]  string & strValue:数据值
	* @param[out] PRO_FRAME_DATA &vData: 保存数据结构信息
	* @return     void
	*/
	virtual void __AddOtherDataToStruct(IN int nDataType,IN string & strValue,OUT PRO_FRAME_DATA &vData);

	/**
	* @brief		将子站系统定义的母线接线方式编码，转成103编码。			
	* @param[in]    int nTyp：子站系统定义的母线接线方式编码 
	* @return		对应103编码.
	* @note 
	**/
	virtual int _CvtBusTypTo103Code(IN int nTyp);

	/**
	* @brief		将系统定义的装置类型枚举转成对应103编码.			
	* @param[in]    TSECONDDEV_TYPE e_psrtype:系统装置类型.
	* @return		对应103编码.
	**/
	virtual int _CvtIedTypeTo103Code(IN int e_psrtype);

	/**
	* @brief		将系统定义的装置运行状态枚举转成对应103编码.			
	* @param[in]    TOPRAMODE e_psrtype:装置运行状态
	* @return		对应103编码.
	**/
	virtual int _CvtIedRunStateTo103Code(IN TOPRAMODE e_psrtype);

	/**
	* @brief		将系统内定义的电压等级枚举转成对应电压数值.			
	* @param[in]  
	* @return		0-执行成功；其他-执行失败
	* @note 
	**/
	virtual int _CvtVoltageTo103Code(IN TVOLTAGE e_voltage);

	/**
	* @brief		根据断路器类型,转出103设备类型编号_南网103			
	* @param[in]    TBRKER_TYPE e_breaktyp:一次设备类型
	* @return		设备类型编号
	**/
	virtual int _CvtBreakTypTo103Code(IN TBRKER_TYPE e_breaktyp);

	/**
	* @brief		根据刀闸类型,转出103设备类型编号_南网103			
	* @param[in]    TDISCONNECTOR_TYPE e_distype:一次设备类型
	* @return		设备类型编号
	**/
	virtual int _CvtDiscnnectorTypTo103Code(IN TDISCONNECTOR_TYPE e_distype);

	/**
	* @brief		根据变压器类型,转出103设备类型编号_南网103			
	* @param[in]    TTRANS_TYPE e_transtype:一次设备类型
	* @return		设备类型编号
	* @note			除指明为三绕组外，其余默认皆为双绕组。
	**/
	virtual int _CvtTransformerTypTo103Code(IN TTRANS_TYPE e_transtype);

	/**
	* @brief		将FPT/JPT的字符串值(AB,AG...)转成103规约按位标识的值.			
	* @param[in]    char * cValue
	* @return		0-执行成功；其他-执行失败
	**/
	virtual u_int8 _CvtPhaseStrToByte(IN char * cValue);

	/**
	* @brief		根据命令获得对应LD的全部配置			
	* @param[in]    PRO_FRAME_BODY * pCmd:规约命令
	* @return		const LD_TB * 命令中对应设备的LD配置
	**/
	virtual const LD_TB * __GetLdAllCfgByCmd(IN PRO_FRAME_BODY * pCmd);

	/**
	* @brief		获取数据库中配置的子站自身的CPU号.			
	* @return		0-执行成功；其他-执行失败
	**/
	virtual void GetCpuOfStnSelf();
	
	////////////////////////////////////////////////////////////////////////私有方法
private:


	////////////////////////////////////////////////////////////////////////保护成员
protected:

	/** @brief         模型查询实例*/
	INXEcSSModelSeek  * m_pModelSeek;

	/** @brief         时间格式是否为CP56格式*/
	bool               m_bCP56Time;

	/** @brief         报文中是否带接收时间*/
	bool               m_bIsHaveRcvTime;

	/** @brief         传输原因是否受品质因素影响*/
	bool               m_bCotByQuality;

	/** @brief         上传故障参数的格式*/
	bool               m_bSendFaultParamWithGeneric;

	/** @brief         是否支持CPU*/
	bool               m_bSupportCpu;

	/** @brief         Csg日志内容结构体*/
	CSG_LOG_DATA_UNIT m_CsgLogDataUnit;
    
	/** @brief		   子站本身的CPU号,默认为0*/
	int					m_nCpuOfStnSelf;
	
	////////////////////////////////////////////////////////////////////////私有成员
private:

	/** @brief         ASDU可变部分最大长度*/
	int                m_nAsduVarDataMaxLen;

	/** @brief         不订阅设备列表*/
	LIST_NOT_ORDER *    m_pNotOrderDevList;


};


/** @} */ //OVER



#endif  // _H_NXECPROASDU_H_