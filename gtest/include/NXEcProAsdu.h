/**********************************************************************
* NXEcProAsdu.h         author:jjl      date:23/10/2013            
*---------------------------------------------------------------------
*  note:IEC103/104ASDU处理结点类头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXECPROASDU_H_ 
#define _H_NXECPROASDU_H_

/** @brief         时间信息*/
typedef struct _ASDU_TIME
{
	/** @brief         信息发生时间(UTC秒)*/
	time_t nInfoHappenUtc;
	/** @brief         信息发生时间毫秒*/
	int16 nInfoHappenMs;
	/** @brief         相对时间(信息发生相对于设备启动)*/
	int16  nRetTime;	
	/** @brief         信息接收时间(UTC秒)*/
	time_t nInfoRcvUtc;
	/** @brief         信息接收时间毫秒*/
	int16 nInfoRcvMs;

	_ASDU_TIME()
	{
		nInfoHappenUtc = 0;
		nInfoHappenMs  = 0;
		nRetTime       = 0;
		nInfoRcvUtc    = 0;
		nInfoRcvMs     = 0;
	}

}ASDU_TIME;

/** @brief         信息体标识符*/
typedef struct _ASDU_INFO_OBJ
{
	/** @brief         功能类型*/
	u_int8 nFun;
	/** @brief         信息序号*/
	u_int8 nInf;
	/** @brief         DPI值*/
	u_int8 nDpi;

	_ASDU_INFO_OBJ()
	{
		nFun = 0;
		nInf = 0;
		nDpi = 0;
	}
}ASDU_INFO_OBJ;

/** @brief         信息体标识对象列表*/
typedef list<ASDU_INFO_OBJ> ASDU_INFO_OBJ_LIST;
#endif  // _H_NXECPROASDU_H_