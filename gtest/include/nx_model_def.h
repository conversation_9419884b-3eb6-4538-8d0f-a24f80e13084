/*! @file
********************************************************************************
<PRE>
模块名       : <nx模型结构定义>
文件名       : <nx_model_def.h>
相关文件     : <与此文件相关的其它文件>
文件实现功能 : <定义nx模型的结构> 
作者         : <李金梅>
版本         : <1.0.0>
--------------------------------------------------------------------------------
多线程安全性 : <是/否>[，说明]
异常时安全性 : <是/否>[，说明]
--------------------------------------------------------------------------------
备注         : <其它说明>
--------------------------------------------------------------------------------
修改记录 : 
日 期        版本     修改人              修改内容
2013/08/22   1.0.0      李金梅			  创建
</PRE>
********************************************************************************

* 项目1
  - 项目1.1
  - 项目1.2

================================================================================
* 项目2
  - 项目2.1
  - 项目2.2
....

*******************************************************************************/ 

#ifndef NX_MODEL_DEF_H_0000000000000000
#define NX_MODEL_DEF_H_0000000000000000

#include <list>
#include <string>
using namespace std;

const char cnx_model_version[] = "1.0.0";

/********************************************************************************************************/
/* 表名定义                                                                                             */
/********************************************************************************************************/
/** @brief       一次设备母表名*/
const char cprim_tb[] = "nx_t_primequipment";
/** @brief       线路表*/
const char cline_tb[] = "nx_t_linesegment";
/** @brief       断路器表名*/
const char cbreaker_tb[] = "nx_t_breaker";
/** @brief       电力变压器表名*/
const char ctrans_tb[] = "nx_t_powertransformer";
/** @brief       刀闸表名*/
const char cdisconn_tb[] = "nx_t_disconnector";
/** @brief       二次设备IED表名*/
const char cied_tb[] = "nx_t_ied";
/** @brief       二次设备IED类型表名*/
const char ciedtype_tb[] = "nx_t_ied_type";
/** @brief       逻辑设备表名*/
const char cld_tb[] = "nx_t_ied_ld";
/** @brief       定值区表名*/
const char csgzone_tb[] = "nx_t_ied_sgzone_cfg";
/** @brief       定值表名*/
const char csg_tb[] = "nx_t_ied_sg_cfg";
/** @brief       软压板表名*/
const char csoftstrap_tb[] = "nx_t_ied_softstrap_cfg";
/** @brief       硬压板表名*/
const char chardstrp_tb[] = "nx_t_ied_hardstrap_cfg";
/** @brief       模拟量表名*/
const char canalog_tb[] = "nx_t_ied_analog_cfg";
/** @brief       事件表名*/
const char cevent_tb[] = "nx_t_ied_event_cfg";
/** @brief       告警表名*/
const char calarm_tb[] = "nx_t_ied_alarm_cfg";
/** @brief       IED逻辑设备下的故障参数表名*/
const char cfaulttag_tb[] = "nx_t_ied_faulttag_cfg";
/** @brief       IED录波模拟量通道配置表名*/
const char cosc_ai_tb[] = "nx_t_ied_osc_ai_cfg";
/** @brief       IED录波开关量通道配置表名*/
const char cosc_di_tb[] = "nx_t_ied_osc_di_cfg";
/** @brief       通讯规约配置表名*/
const char cprotocol_tb[] = "nx_t_protocol_cfg";
/** @brief       系统基本参数配置表名*/
const char cbasic_cfg_tb[] = "nx_t_basic_cfg";
/** @brief       系统业务功能超时配置表名*/
const char ctimeout_tb[] = "nx_t_timeout_cfg";
/** @brief       通讯状态变化原因配置表名*/
const char ccommu_chg_reason_tb[] = "nx_t_commu_status_chg_reason_cfg";
/** @brief       数据库版本记录表名*/
const char cdb_ver_tb[] = "nx_t_db_his_ver";
/** @brief       子控制区域表名*/
const char csub_area_tb[] = "nx_t_subcontrolarea";
/** @brief       变电站表名*/
const char csubstation_tb[] = "nx_t_substation";
/** @brief       间隔表名*/
const char cbay_tb[] = "nx_t_bay";
/** @brief       对外通信客户端配置表名*/
const char cclient_tb[] = "nx_t_ecu_client_cfg";
/** @brief       对外通信客户端通道配置表名*/
const char cchannel_db[] = "nx_t_ecu_channel_cfg";
/** @brief       对外通信客户端不订阅设备配置表名*/
const char cnot_order_db[] = "nx_t_ecu_dev_not_order_cfg";
/** @brief       对外通信客户端信息订制表名*/
const char corder_db[] = "nx_t_ecu_info_order_cfg";
/** @brief       对外通信监听服务配置表名*/
const char clisten_tb[] = "nx_t_ecu_listen_cfg";
/** @brief       对外通信组标题配置表名*/
const char cgtitile_tb[] = "nx_t_ecu_gtitle_cfg";
/** @brief       线路视图*/
const char cline_view[] = "nx_t_line_view";
/** @brief       断路器视图*/
const char cbreaker_view[] = "nx_t_breaker_view";
/** @brief       电力变压器视图*/
const char ctrans_view[] = "nx_t_trans_view";
/** @brief       刀闸视图*/
const char cdisconn_view[] = "nx_t_disconn_view";

/** @brief  IED当前定值数据表*/
const char c_ied_sg_curdata_tb[] = "nx_t_ied_sg_curdata";
/** @brief  IED历史定值数据表*/
const char c_ied_sg_hisdata_tb[] = "nx_t_ied_sg_data";
/** @brief  IED定值变化告警数据表*/
const char c_ied_sg_chg_rpt_tb[] = "nx_t_ied_sg_alarm_data";
/** @brief  IED软压板变化告警数据表*/
const char c_ied_softtrap_chg_rpt_tb[] = "nx_t_ied_softstrap_data";
/** @brief  IED硬压板变化告警数据表*/
const char c_ied_hardtrap_chg_rpt_tb[] = "nx_t_ied_hardstrap_data";
/** @brief  动作事件数据历史数据表*/
const char c_ied_event_rpt_tb[] = "nx_t_ied_event_data";
/** @brief  告警事件数据历史数据表*/
const char c_ied_alarm_rpt_tb[] = "nx_t_ied_alarm_data";
/** @brief  IED历史录波列表*/
const char c_ied_comtrade_list_tb[] = "nx_t_ied_comtrade_list";
/** @brief  IED运行历史状态表*/
const char c_ied_opr_status_tb[] = "nx_t_ied_opr_status";
/** @brief  对外通信通道状态历史记录表*/
const char c_ecu_link_status_tb[] = "nx_t_ecu_link_status";
/** @brief  IED接入通讯状态历史记录表*/
const char c_ied_commu_status_tb[] = "nx_t_ied_commu_status";
/** @brief  IED模拟量输入越限告警表*/
const char c_ied_analog_alarm_data_tb[] = "nx_t_ied_analog_alarm_data";

#define  NX_APP_SQL_MAX_LENGTH  900000  //9K

/********************************************************************************************************/
/* 设备容器类结构定义                                                                                   */
/********************************************************************************************************/

/** @brief       电压等级*/
enum TVOLTAGE
{
	KV_1000		= 1,/**<  1000kV */
	KV_800		= 2,/**<  800kV */
	KV_750		= 3,/**<  750kV */
	KV_500		= 4,/**<  500kV */
	KV_330		= 5,/**<  330kV */
	KV_220		= 6,/**<  220kV */
	KV_110		= 7,/**<  110kV */
	KV_66		= 8,/**<  66kV */
	KV_35		= 9,/**<  35kV */
	KV_10		= 10,/**<  10kV */
	KV_6		= 11/**<  6kV */
};

/** @brief       间隔表字段定义*/
typedef struct _BAY_TB
{
	/** @brief       间隔编号*/
	int n_obj_id;
	/** @brief       所属的变电站*/
	int n_station_obj;
	/** @brief       间隔名称*/
	string str_aliasname;
	/** @brief       电压等级*/
	TVOLTAGE e_voltage;
	/** @brief       统一资源标识*/
	string str_rdfid;
	/** @brief       外部系统编号*/
	string str_ex_rdfid;
	/** @brief       图形文件路径*/
	string str_graphpath;
	/** @brief       整型备用字段*/
	int n_nbackup;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       注释*/
	string str_notes;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_BAY_TB()
	{
		n_obj_id		= 1;
		n_station_obj	= 0;
		e_voltage		= KV_1000;
		n_nbackup		= 0;
		p_backup		= NULL;
	}
	
}BAY_TB;

/** @brief       间隔表字段链表定义*/
typedef list<BAY_TB> LIST_BAY;

/** @brief       变电站表字段定义*/
typedef struct _SUBSTATION_TB
{
	/** @brief       变电站编号*/
	int n_obj_id;
	/** @brief       变电站名称*/
	string str_aliasname;
	/** @brief       所属子站控制域*/
	int n_area_obj;
	/** @brief       电压等级*/
	TVOLTAGE e_valtage;
	/** @brief       图形文件路径*/
	string str_graphpath;
	/** @brief       生产厂家*/
	string str_manufacturer;
	/** @brief       型号*/
	string str_model;
	/** @brief       软件版本*/
	string str_softver;
	/** @brief       投运时间*/
	string str_opratm;
	/** @brief       103地址*/
	int n_outaddr103;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       统一资源标识*/
	string str_rdfid;
	/** @brief       外部系统编号*/
	string str_ex_rdfid;
	/** @brief       整型备用字段*/
	int n_nbackup;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       注释*/
	string str_notes;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       间隔链表*/
	LIST_BAY lst_bay;
	/** @brief       默认构造函数*/
	_SUBSTATION_TB()
	{
		n_obj_id		= 1;
		n_area_obj		= 0;
		e_valtage		= KV_1000;
		n_outaddr103	= 0;
		n_outgroup103	= 0;
		n_nbackup		= 0;
		p_backup		= NULL;
	}
	
}SUBSTATION_TB;

/** @brief       变电站表字段链表定义*/
typedef list<SUBSTATION_TB> LIST_SUBSTATION;

/** @brief       子控制区域表字段链表定义*/
typedef struct _SUB_AREA_TB
{
	/** @brief       地区编号*/
	int n_obj_id;
	/** @brief       地区名称*/
	string str_aliasname;
	/** @brief       用户给定的全网的统一编号*/
	string str_rdfid;
	/** @brief       描述*/
	string str_ex_rdfid;
	/** @brief       整型备用字段*/
	int n_nbackup;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       注释*/
	string str_notes;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       变电站链表*/
	LIST_SUBSTATION lst_substation;
	/** @brief       默认构造函数*/
	_SUB_AREA_TB()
	{
		n_obj_id	= 1;
		n_nbackup	= 0;
		p_backup	= NULL;
	}

}SUB_AREA_TB;

/** @brief       子控制区域表字段链表定义*/
typedef list<SUB_AREA_TB> LIST_SUB_AREA;

/********************************************************************************************************/
/* 一次设备表结构定义																					*/
/********************************************************************************************************/

/** @brief       一次设备类型*/
enum TPRIM_TYPE
{
	PRIM__BUS			= 1,/**<  母线 */
	PRIM_LINE			= 2,/**<  线路 */
	PRIM_BREAKER		= 3,/**<  开关 */
	PRIM_TRANSFORMER	= 4,/**<  变压器 */
	PRIM_DISCNNECTOR	= 6,/**<  刀闸 */
	PRIM_GENERATOR		= 7,/**<  发电机 */
	PRIM_REACTOR		= 8,/**<  电抗器 */
	PRIM_COMPENSATOR	= 9,/**<  电容器 */
	PRIM_OTHER			= 10/**<  其它 */
};

/** @brief       运行状态*/
enum TOPRAMODE
{
	OPRAMODE_TEST		= 0,/**<  检修 */
	OPRAMODE_STOP		= 1,/**<  停运 */
	OPRAMODE_RUN		= 2,/**<  投运 */
	OPRAMODE_NO_ACCESS	= 3,/**<  未接入 */
	OPRAMODE_DEBUG		= 4/**<  调试(对码表) */
};

/** @brief       线路表nx_t_linesegment*/
typedef struct _LINESEGMENT_TB
{
	/** @brief       线路长度 单位为km*/
	float f_length;
	/** @brief       参数比例*/
	int n_paramscale;
	/** @brief       正序串联电阻*/
	float f_liner;
	/** @brief       正序串联电抗*/
	float f_linex;
	/** @brief       零序串联电阻*/
	float f_line0;
	/** @brief       零序串联电抗*/
	float f_linex0;
	/** @brief       正序电容*/
	float f_linegch;
	/** @brief       零序电容*/
	float f_lineg0ch;
	/** @brief       最大电流*/
	float f_linemaxi;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_LINESEGMENT_TB()
	{
		f_length		= 100.0f;
		n_paramscale	= 1;
		f_liner			= 1.0f;
		f_linex			= 10.0f;
		f_line0			= 1.0f;
		f_linex0		= 30.0f;
		f_linegch		= 1.0f;
		f_lineg0ch		= 1.0f;
		f_linemaxi		= 5.0f;
		p_backup		= NULL;
	}

}LINESEGMENT_TB;

/** @brief       线路表字段链表定义*/
typedef list<LINESEGMENT_TB> LIST_LINE;

/** @brief       断路器类型*/
enum TBRKER_TYPE
{
	BRKER_TYPE_UNKOWN		= 0,/**<  未知 */
	LINE_BRKER				= 1,/**<  线路开关 */
	MID_BRKER_3_2			= 2,/**<  3/2接线中开关 */
	DOUBLE_BUS_BRKER		= 3,/**<  双母线母联开关 */
	SEGMENT_BRKER			= 4,/**<  单母线分段开关 */
	BYPASS_BRKER			= 5,/**<  旁路开关 */
	OTHER_BRKER				= 6/**<  其它 */
};

/** @brief       断路器表nx_t_breaker*/
typedef struct _BREAKER_TB
{
	/** @brief       断路器类型*/
	TBRKER_TYPE e_brkertype;
	/** @brief       默认状态*/
	int n_normalopen;
	/** @brief       当前闭合状态*/
	int n_currentstate;
	/** @brief       最近一次闭合时间*/
	string str_ondate;
	/** @brief       开合次数*/
	int n_oncounts;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_BREAKER_TB()
	{
		e_brkertype		= LINE_BRKER;
		n_normalopen	= 1;
		n_currentstate	= 1;
		n_oncounts		= 0;
		p_backup		= NULL;
	}

}BREAKER_TB;

/** @brief       断路器表字段链表定义*/
typedef list<BREAKER_TB> LIST_BREAKER;

/** @brief       变压器类型*/
enum TTRANS_TYPE
{
	AUTOTRANSFORMER		= 1,/**<  自耦变 */
	TOW_WINDING			= 2,/**<  双绕组 */
	THREE_WINDING		= 3,/**<  三绕组 */
	MULTI_WINDING		= 4/**<  多绕组 */
};

/** @brief       变压器表nx_t_powertransformer*/
typedef struct _TRANSFORMER_TB
{
	/** @brief       变压器类型*/
	TTRANS_TYPE e_transtype;
	/** @brief       接线方式，如星三角接法等*/
	int n_connecttype;
	/** @brief       高压侧电压*/
	TVOLTAGE e_voltageh;
	/** @brief       中压侧电压*/
	TVOLTAGE e_voltagem;
	/** @brief       低压侧电压*/
	TVOLTAGE e_voltagel;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_TRANSFORMER_TB()
	{
		e_transtype	= AUTOTRANSFORMER;
		n_connecttype = 0;
		e_voltageh	= KV_1000;
		e_voltagem	= KV_1000;
		e_voltagel	= KV_1000;
		p_backup	= NULL;
	}

}TRANSFORMER_TB;

/** @brief       变压器表字段链表定义*/
typedef list<TRANSFORMER_TB> LIST_TRANSFORMER;

/** @brief       刀闸类型*/
enum TDISCONNECTOR_TYPE
{
	NOT_GROUND_DISCONNECTOR		= 0,/**<  非接地刀闸 */
	GROUND_DISCONNECTOR			= 1/**<  接地刀闸 */
};

/** @brief       刀闸表nx_t_disconnector*/
typedef struct _DISCONNECTOR_TB
{
	/** @brief       刀闸类型*/
	TDISCONNECTOR_TYPE e_distype;
	/** @brief       默认状态*/
	int n_normalopen;
	/** @brief       当前闭合状态*/
	int n_currentstate;
	/** @brief       最近一次闭合时间*/
	string str_ondates;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_DISCONNECTOR_TB()
	{
		e_distype		= NOT_GROUND_DISCONNECTOR;
		n_normalopen	= 1;
		n_currentstate	= 1;
		p_backup		= NULL;
	}

}DISCONNECTOR_TB;

/** @brief       刀闸表表字段链表定义*/
typedef list<DISCONNECTOR_TB> LIST_DISCONNECTOR;

/** @brief       一次设备母表nx_t_primequipment*/
typedef struct _PRIMEQUIPMENT_TB
{
	/** @brief       对象ID*/
	int n_obj_id;
	/** @brief       设备名称*/
	string str_aliasname;
	/** @brief       所属厂站*/
	int n_station_obj;
	/** @brief       所属间隔*/
	int n_bay_obj;
	/** @brief       类型*/
	TPRIM_TYPE e_psrtype;
	/** @brief       型号*/
	string str_model;
	/** @brief       电压等级*/
	TVOLTAGE e_voltage;
	/** @brief       容量 单位:MVA*/
	float f_capacity;
	/** @brief       连接端点a*/
	string str_terminal_a;
	/** @brief       连接端点b*/
	string str_terminal_b;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       编号-站间103通讯规约中使用的编号*/
	int n_outcode103;
	/** @brief       开关编号1*/
	int n_brker_code1;
	/** @brief       开关编号2*/
	int n_brker_code2;
	/** @brief       当前运行状态*/
	TOPRAMODE e_opramode;
	/** @brief       当前工作状态最后更新时间*/
	string str_opramodetm;
	/** @brief       统一标识器*/
	string str_rdfid;
	/** @brief       外部系统编号*/
	string str_ex_rdfid;
	/** @brief       整形备用*/
	int n_nbackup;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       字符备用3*/
	string str_strbackup3;
	/** @brief       备注*/
	string str_notes;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       线路表字段*/
	LINESEGMENT_TB line_field;
	/** @brief       断路器表字段*/
	BREAKER_TB breaker_field;
	/** @brief       电力变压器表字段*/
	TRANSFORMER_TB trans_field;
	/** @brief       刀闸表字段*/
	DISCONNECTOR_TB disconn_field;
	/** @brief       默认构造函数*/
	_PRIMEQUIPMENT_TB()
	{
		n_obj_id		= 1;
		n_station_obj	= 0;
		n_bay_obj		= 0;
		e_psrtype		= PRIM_LINE;
		e_voltage		= KV_1000;
		f_capacity		= 0.0f;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_outcode103	= 1;
		n_brker_code1	= 0;
		n_brker_code2	= 0;
		e_opramode		= OPRAMODE_STOP;
		n_nbackup		= 0;
		p_backup		= NULL;
	}
	
}PRIMEQUIPMENT_TB;

/** @brief       一次设备母表字段链表定义*/
typedef list<PRIMEQUIPMENT_TB> LIST_PRIM;

/********************************************************************************************************/
/* 二次设备表结构定义																					*/
/********************************************************************************************************/

/** @brief       二次设备类型*/
enum TSECONDDEV_TYPE
{
	SUB_STATION					= 0,	/**<  子站 */
	LINE_PRO					= 1,	/**<  线路保护 */
	TRANSFORMER_PRO				= 2,	/**<  变压器保护 */
	BUS_PRO						= 3,	/**<  母差保护 */
	BUS_CONNECT_PRO				= 4,	/**<  母联保护 */
	GENERATOR_PRO				= 5,	/**<  发变组保护 */
	BRKER_PRO					= 6,	/**<  开关保护 */
	COMPENSATOR_PRO				= 7,	/**<  电容器保护 */
	REACTOR_PRO					= 8,	/**<  电抗器保护  */
	FAULT_RECORDER				= 10,	/**<  故障录波器 */
	NET_MSG_RECORDER			= 11,	/**<  网络报文记录仪 */
	MEAS_CTRL_DEV				= 12,	/**<  测控装置 */
	ELECTRIC_METER				= 13,	/**<  电能表 */
	SAFE_STABLE_DEV				= 14,	/**<  安稳装置 */
	LOWCYCLE_DELOAD_DEV			= 15,	/**<  低周减载装置 */
	LOWFREQ_SPLIT_DEV			= 16,	/**<  低频解列装置   */
	GPS_DEV						= 17,	/**<  GPS装置 */
	MU_DEV						= 18,	/**<  合并单元 */
	IT_DEV						= 19,	/**<  智能终端 */
	SWITCH_DEV					= 20,	/**<  交换机 */
	ROUTER_DEV					= 21,	/**<  路由器 */
	NET_STORAGE					= 22	/**网络存储器 */
};

/** @brief       定值区号表nx_t_ied_sgzone_cfg*/
typedef struct _SGZONE_TB
{
	/** @brief       定值区号*/
	int n_zone_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       值类型 1：当前区  2：编辑区*/
	int n_psrtype;
	/** @brief       定值区名称*/
	string str_aliasname;
	/** @brief       下限*/
	int f_downlimit;
	/** @brief       上限*/
	int f_uplimit;
	/** @brief       当前值*/
	int n_curvalue;
	/** @brief       当前值更新时间*/
	string str_curvaluetm;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       ied103组号*/
	int n_iedgroup103;
	/** @brief       ied103条目号*/
	int n_ieditem103;
	/** @brief       基准值*/
	int n_basevalue;
	/** @brief       基准值更新时间*/
	string str_basevaluetm;
	/** @brief       基准值来源*/
	int n_basevaluesrc;
	/** @brief       接入属性1*/
	string str_holdattr_1;
	/** @brief       接入属性2*/
	string str_holdattr_2;
	/** @brief       接入属性3*/
	string str_holdattr_3;
	/** @brief       接入属性4*/
	string str_holdattr_4;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_SGZONE_TB()
	{
		n_zone_code		= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		n_psrtype		= 1;
		f_downlimit		= 0;
		f_uplimit		= 32;
		n_curvalue		= 0;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		n_basevaluesrc	= 0;
		p_backup		= NULL;
	}

}SGZONE_TB;

/** @brief       定值区号表字段链表定义*/
typedef list<SGZONE_TB> LIST_SGZONE;

/** @brief       值类型*/
enum TVALUE_TYPE
{
	VALUE_FLOAT		= 0,/**<  浮点型 */
	VALUE_INT		= 1,/**<  整形 */
	VALUE_HEX		= 2,/**<  十六进制 */
	VALUE_CHAR		= 3,/**<  字符串 */
	VALUE_BIN		= 4/**<  二进制 */

};

/** @brief       定值功能类型*/
enum TSG_TYPE
{
	SG_COM				= 0,/**<  普通定值 */
	SG_CURSGZONE		= 1,/**<  当前定值区 */
	SG_EDTSGZONE		= 2,/**<  编辑定值区 */
	SG_TEST				= 3,/**<  检修标志 */
	SG_RECLOSING		= 4,/**<  重合闸 */
	SG_LTD_DIFF_PRO		= 5,/**<  纵联差动保护类 */
	SG_DIST_PRO			= 6,/**<  距离保护类 */
	SG_0				= 7,/**<  零序类 */
	SG_FUN_OPEN_CLOSE	= 8/**<  功能投退类 */
};

/** @brief       定值表nx_t_ied_sg_cfg*/
typedef struct _SG_TB
{
	/** @brief       定值编号*/
	int n_sg_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       定值名称*/
	string str_aliasname;
	/** @brief       定值名称缩写*/
	string str_abbrename;
	/** @brief       数据类型*/
	TVALUE_TYPE e_psrdatatype;
	/** @brief       控制字使用及描述*/
	string str_ctrlwordlist;
	/** @brief       单位*/
	string str_sgunit;
	/** @brief       精度*/
	string str_sgpresion;
	/** @brief       下限*/
	float f_downlimit;
	/** @brief       上限*/
	float f_uplimit;
	/** @brief       步长*/
	float f_stepwidth;
	/** @brief       折算率*/
	float f_fratio;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       ied103组号*/
	int n_iedgroup103;
	/** @brief       ied103条目号*/
	int n_ieditem103;
	/** @brief       接入属性1*/
	string str_holdattr_1;
	/** @brief       接入属性2*/
	string str_holdattr_2;
	/** @brief       接入属性3*/
	string str_holdattr_3;
	/** @brief       接入属性4*/
	string str_holdattr_4;
	/** @brief       功能类型*/
	TSG_TYPE e_psrfuntpye;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       基准值*/
	int n_basevalue;
	/** @brief       基准值更新时间*/
	string str_basevaluetm;
	/** @brief       基准值来源*/
	int n_basevaluesrc;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_SG_TB()
	{
		n_sg_code		= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		e_psrdatatype	= VALUE_FLOAT;
		str_sgunit		= "V";
		f_downlimit		= 0.0f;
		f_uplimit		= 65535.0f;
		f_stepwidth		= 0.1f;
		f_fratio		= 1.0f;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		e_psrfuntpye	= SG_COM;
		n_basevalue		= 1;
		n_basevaluesrc	= 0;
		p_backup		= NULL;
	}

}SG_TB;

/** @brief       定值表字段链表定义*/
typedef list<SG_TB> LIST_SG;

/** @brief       压板功能类型*/
enum TSTRAP_TYPE
{
	SOFT_UNKOWN				= 0,/**<  未知压板 */		
	SOFT_TEST				= 2,/**<  检修标志 */
	SOFT_RECLOSING			= 3,/**<  重合闸 */
	SOFT_LTD_DIFF_PRO		= 4,/**<  纵联差动保护类 */
	SOFT_DIST_PRO			= 5,/**<  距离保护类 */
	SOFT_0					= 6/**<  零序类 */

};

/** @brief       压板表(软压板nx_t_ied_softstrap_cfg、硬压板nx_t_ied_hardstrap_cfg)*/
typedef struct _STRAP_TB
{
	/** @brief       压板编号*/
	int n_strap_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       压板名称*/
	string str_aliasname;
	/** @brief       压板名称缩写*/
	string str_abbrename;
	/** @brief       当前值*/
	int n_curvalue;
	/** @brief       当前值更新时间*/
	string str_curvaluetm;
	/** @brief       更新时间毫秒*/
	int n_curvaluems;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       功能号*/
	int n_outfun103;
	/** @brief       信息序号*/
	int n_outinf103;
	/** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       接入属性1*/
	string str_holdattr_1;
	/** @brief       接入属性2*/
	string str_holdattr_2;
	/** @brief       接入属性3*/
	string str_holdattr_3;
	/** @brief       接入属性4*/
	string str_holdattr_4;
	/** @brief       基准值*/
	int n_basevalue;
	/** @brief       基准值更新时间*/
	string str_basevaluetm;
	/** @brief       基准值来源*/
	int n_basevaluesrc;
	/** @brief       描述*/
	TSTRAP_TYPE e_psrfuntype;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_STRAP_TB()
	{
		n_strap_code	= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		n_curvalue		= -1;
		n_curvaluems	= 0;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_outfun103		= 0;
		n_outinf103		= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		n_basevalue		= 1;
		n_basevaluesrc	= 0;
		e_psrfuntype	= SOFT_UNKOWN;
	}
	
}STRAP_TB;

/** @brief       压板字段链表定义*/
typedef list<STRAP_TB> LIST_STRAP;

enum TPSRFUNTYPE_ANALOG
{
	TPSRFUNTYPE_ANALOG_GENERAL = 0,/**<  一般模拟量 */
	TPSRFUNTYPE_ANALOG_ACQ_VOLTAGE = 1,/**<  采集电压 */
	TPSRFUNTYPE_ANALOG_ACQ_CURRENT = 2,/**<  采集电流 */
	TPSRFUNTYPE_ANALOG_ACQ_ACTIVE_POWER = 3,/**<  采集有功 */
	TPSRFUNTYPE_ANALOG_ACQ_REACTIVE_POWER = 4,/**<  采集无功 */
	TPSRFUNTYPE_ANALOG_ACQ_ELEC_QUANTITY = 5,/**<  采集电量 */
	TPSRFUNTYPE_ANALOG_ACQ_FREQUENCY = 8,/**<  采集频率 */
	TPSRFUNTYPE_ANALOG_ACQ_IMPEDANCE = 9,/**<  采集阻抗 */
	TPSRFUNTYPE_ANALOG_DEV_VOLTAGE = 11,/**<  设备电压 */
	TPSRFUNTYPE_ANALOG_DEV_CURRENT = 12,/**<  设备电流 */
	TPSRFUNTYPE_ANALOG_DEV_POWER_CONSUMPTION = 13,/**<  设备功耗 */
	TPSRFUNTYPE_ANALOG_DEV_TEMPERATURE = 14/**<  设备温度 */
};
enum MONITORTAG_ANALOG
{
	MONITORTAG_ANALOG_NO = 0,/**< 不监视 */
	MONITORTAG_ANALOG_LIMIT = 1,/**< 按设定上下限监视 */
	MONITORTAG_ANALOG_CHGRATE = 2,/**< 按最新值变化率监视 */
	MONITORTAG_ANALOG_LIMIT_CHGRATE = 3/**< 按设定上下限及最新值变化率监视 */
};
/** @brief       模拟量表nx_t_ied_analog_cfg*/
typedef struct _ANALOG_TB
{
	/** @brief       描述*/
	int n_ana_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       模拟量名称*/
	string str_aliasname;
	/** @brief       模拟量名称缩写*/
	string str_abbrename;
	/** @brief       当前值*/
	string str_curvalue;
	/** @brief       当前值更新时间*/
	string str_curvaluetm;
	/** @brief       数据类型*/
	TVALUE_TYPE e_psrdatatype;
	/** @brief       单位*/
	string str_anaunit;
	/** @brief       精度描述*/
	string str_anapresion;
	/** @brief       下限*/
	float f_downlimit;
	/** @brief       上限*/
	float f_uplimit;
	/** @brief       折算率*/
	float f_fratio;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       接入属性1*/
	string str_holdattr_1;
	/** @brief       接入属性2*/
	string str_holdattr_2;
	/** @brief       接入属性3*/
	string str_holdattr_3;
	/** @brief       接入属性4*/
	string str_holdattr_4;
	/** @brief       模拟量类型*/
	TPSRFUNTYPE_ANALOG e_psrfuntype;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       监视标记*/
	MONITORTAG_ANALOG e_monitortag;
	/** @brief       最新值变化门槛*/
	int n_updaterate;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ANALOG_TB()
	{
		n_ana_code		= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
//		n_curvalue		= 0;
		e_psrdatatype	= VALUE_FLOAT;
		str_anaunit		= "V";
		f_downlimit		= 0.0f;
		f_uplimit		= 0.0f;
		f_fratio		= 0.0f;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		e_psrfuntype	= TPSRFUNTYPE_ANALOG_GENERAL;
		e_monitortag    = MONITORTAG_ANALOG_NO;
		n_updaterate    = 20;
		p_backup		= NULL;
	}

}ANALOG_TB;

/** @brief       模拟量字段链表定义*/
typedef list<ANALOG_TB> LIST_ANALOG;

/** @brief       事件功能类型*/
enum TEVENT_TYPE
{
	EVENT_UNKOWN			= 0,/**<  未知事件 */		
	EVENT_RECLOSING			= 3,/**<  重合闸动作 */
	EVENT_LTD_DIFF_PRO		= 4,/**<  纵联差动保护动作 */
	EVENT_DIST_PRO			= 5,/**<  距离保护动作 */
	EVENT_3_DIST_PRO		= 6,/**<  三段距离保护动作 */
	EVENT_0_DIST_PRO		= 7,/**<  零序距离动作 */
	EVENT_DEV_FAULT			= 100,/**<  装置异常 */
	EVENT_CIRCUIT_FAULT		= 101,/**<  回路异常 */
	EVENT_CHANNEL_FAULT		= 102,/**<  通道异常 */
	EVENT_OTHER_FAULT		= 103,/**<  其它异常 */
	EVENT_DEV_TEST			= 104/**<  装置检修 */

};

/** @brief       事件表nx_t_ied_event_cfg*/
typedef struct _EVENT_TB
{
	/** @brief       事件ID*/
	int n_event_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       事件名称*/
	string str_aliasname;
	/** @brief       事件名称缩写*/
	string str_abbrename;
	/** @brief       事件级别1: 一级事件   2:二级事件 3：三级事件*/
	int n_psrgradetype;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       功能号*/
	int n_outfun103;
	/** @brief       信息序号*/
	int n_outinf103;
	/** @brief		站间101通讯中使用的信息号*/
	int n_inf101;
	/** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       保留属性1*/
	string str_holdattr_1;
	/** @brief       保留属性2*/
	string str_holdattr_2;
	/** @brief       保留属性3*/
	string str_holdattr_3;
	/** @brief       保留属性4*/
	string str_holdattr_4;
	/** @brief       事件功能类型*/
	TEVENT_TYPE e_psrfuntype;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_EVENT_TB()
	{
		n_event_code	= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		n_psrgradetype	= 1;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_outfun103		= 0;
		n_outinf103		= 0;
		n_inf101		= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		e_psrfuntype	= EVENT_UNKOWN;
		p_backup		= NULL;
	}

}EVENT_TB;

/** @brief       事件表字段链表定义*/
typedef list<EVENT_TB> LIST_EVENT;

/** @brief       告警表nx_t_ied_event_cfg*/
typedef struct _ALARM_TB
{
	/** @brief       告警ID*/
	int n_alarm_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       事件名称*/
	string str_aliasname;
	/** @brief       事件名称缩写*/
	string str_abbrename;
	/** @brief       事件级别1: 一级事件   2:二级事件 3：三级事件*/
	int n_psrgradetype;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       功能号*/
	int n_outfun103;
	/** @brief       信息序号*/
	int n_outinf103;
	/** @brief		站间101通讯中使用的信息号*/
	int n_inf101;
	/** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       保留属性1*/
	string str_holdattr_1;
	/** @brief       保留属性2*/
	string str_holdattr_2;
	/** @brief       保留属性3*/
	string str_holdattr_3;
	/** @brief       保留属性4*/
	string str_holdattr_4;
	/** @brief       事件功能类型*/
	TEVENT_TYPE e_psrfuntype;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ALARM_TB()
	{
		n_alarm_code	= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		n_psrgradetype	= 1;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_outfun103		= 0;
		n_outinf103		= 0;
		n_inf101		= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		e_psrfuntype	= EVENT_UNKOWN;
		p_backup		= NULL;
	}
	
}ALARM_TB;

/** @brief       告警表字段链表定义*/
typedef list<ALARM_TB> LIST_ALARM;

/** @brief       特征量功能类型*/
enum TFAULTTAG_TYPE
{
	FAULTTAG_UNKOWN			= 0,/**<  未配置 */		
	ELEC_FAULT_NUM			= 1,/**<  电网故障序号 */
	FAULT_NUM				= 2,/**<  故障序号 */
	FAULT_LOCATION			= 3,/**<  故障测距 */
	FAULT_PHASE				= 4,/**<  故障相别 */
	TRIP_PHASE				= 5,/**<  跳闸相别 */
	CURRENT_PREFAULT		= 10,/**<  故障前电流 */
	ACT_POWER_PREFAULT		= 11,/**<  故障前有功 */
	UNACT_POWER_PREFAULT	= 12,/**<  故障前无功 */
	MAX_CURRENT_FAULT		= 13,/**<  故障时最大故障电流 */
	MAX_CURRENT_0			= 14,/**<  故障时最大零序电流 */
	FAULT_R					= 15,/**<  故障时电阻 */
	FAULT_X					= 16/**<  故障时电抗 */
	
};

/** @brief      故障参数表nx_t_ied_faulttag_cfg*/
typedef struct _FAULTTAG_TB
{
	/** @brief       特征量编号*/
	int n_tag_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       特征量名称*/
	string str_aliasname;
	/** @brief       特征量名称缩写*/
	string str_abbrename;
	/** @brief       数据类型*/
	TVALUE_TYPE e_psrdatatype;
	/** @brief       单位*/
	string str_unit;
	/** @brief       精度*/
	string str_tagpresion;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       101信息编号*/
	int n_inf101;
	/** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       保留属性1*/
	string str_holdattr_1;
	/** @brief       保留属性2*/
	string str_holdattr_2;
	/** @brief       保留属性3*/
	string str_holdattr_3;
	/** @brief       保留属性4*/
	string str_holdattr_4;
	/** @brief       特征量功能类型*/
	TFAULTTAG_TYPE e_psrfuntype;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_FAULTTAG_TB()
	{
		n_tag_code		= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		e_psrdatatype	= VALUE_CHAR;
		str_unit		= "V";
		str_tagpresion	= "2.2";
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_inf101		= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		e_psrfuntype	= FAULTTAG_UNKOWN;
		p_backup		= NULL;
	}

}FAULTTAG_TB;

/** @brief       故障参数表字段链表定义*/
typedef list<FAULTTAG_TB> LIST_FAULTTAG;

/** @brief       描述*/
enum TCHANNEL_TYPE
{
	CHANNEL_CURRENT			= 1,/**<  电流 */
	CHANNEL_VOLTAGE			= 2,/**<  电压 */
	CHANNEL_POWER			= 3,/**<  功率 */
	CHANNEL_HIGH_FREQUENCY	= 4/**<  高频 */

};

/** @brief       IED录波模拟量通道表nx_t_ied_osc_ai_cfg*/
typedef struct _OSC_AI_TB
{
	/** @brief       通道编号*/
	int n_ai_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       模拟量通道中文名称*/
	string str_aliasname;
	/** @brief       模拟量通道中文名称缩写*/
	string str_abbrename;
	/** @brief       通道类型*/
	TCHANNEL_TYPE e_psrtype;
	/** @brief       通道比例因子*/
	float f_a;
	/** @brief       通道偏移量*/
	float f_b;
	/** @brief       通道相别*/
	string str_phase;
	/** @brief       单位*/
	string str_unit;
	/** @brief       下限*/
	float f_downlimit;
	/** @brief       上限*/
	float f_uplimit;
	/** @brief       CT/PT变比*/
	float f_ctptratio;
	/** @brief       一次值标记 0:二次值  1:一次值*/
	int n_primtag;
	/** @brief       关联的一次设备*/
	int n_equ_obj;
	/** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       保留属性1*/
	string str_holdattr_1;
	/** @brief       保留属性2*/
	string str_holdattr_2;
	/** @brief       保留属性3*/
	string str_holdattr_3;
	/** @brief       保留属性4*/
	string str_holdattr_4;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_OSC_AI_TB()
	{
		n_ai_code		= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		e_psrtype		= CHANNEL_CURRENT;
		f_a				= 1.0f;
		f_b				= 0.0f;
		str_phase		= "A";
		str_unit		= "V";
		f_downlimit		= -32767.0f;
		f_uplimit		= 32727.0f;
		f_ctptratio		= 1.0f;
		n_primtag		= 0;
		n_equ_obj		= 1;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		p_backup		= NULL;
	}

}OSC_AI_TB;

/** @brief       IED录波模拟量通道表字段链表定义*/
typedef list<OSC_AI_TB> LIST_OSC_AI;

/** @brief       描述*/
enum TOSI_DI_TYPE
{
	BREAKER_CHANGE			= 0,/**<  断路器变位 */
	RECLOSING_PRO_ACT		= 1,/**<  重合闸保护动作 */
	PRO_TRIP				= 2,/**<  保护跳闸 */
	POR_ALARM				= 3,/**<  保护告警 */
	IED_TEST				= 4/**<  IED检修状态 */
};

/** @brief       IED录波开关量通道表nx_t_ied_osc_di_cfg*/
typedef struct _OSC_DI_TB
{
	/** @brief       通道编号*/
	int n_di_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD编号*/
	int n_ld_code;
	/** @brief       开关量通道中文名称*/
	string str_aliasname;
	/** @brief       开关量通道中文名称缩写*/
	string str_abbrename;
	/** @brief       通道相别*/
	string str_phase;
	/** @brief       默认状态*/
	int n_normalopen;
	/** @brief       关联的一次设备*/
	int n_equ_obj;
	//** @brief       IED的103组号*/
	int n_iedgroup103;
	/** @brief       IED的103条目号*/
	int n_ieditem103;
	/** @brief       保留属性1*/
	string str_holdattr_1;
	/** @brief       保留属性2*/
	string str_holdattr_2;
	/** @brief       保留属性3*/
	string str_holdattr_3;
	/** @brief       保留属性4*/
	string str_holdattr_4;
	/** @brief       功能类型*/
	TOSI_DI_TYPE e_psrfuntype;
	/** @brief       字符型备用字段1*/
	string str_strbackup1;
	/** @brief       字符型备用字段2*/
	string str_strbackup2;
	/** @brief       字符型备用字段3*/
	string str_strbackup3;
	/** @brief       指针型备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_OSC_DI_TB()
	{
		n_di_code		= 1;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		str_phase		= "0";
		n_normalopen	= 1;
		n_equ_obj		= 0;
		n_iedgroup103	= 0;
		n_ieditem103	= 0;
		e_psrfuntype	= POR_ALARM;
		p_backup		= NULL;
	}

}OSC_DI_TB;

/** @brief       IED录波开关量通道表字段链表定义*/
typedef list<OSC_DI_TB> LIST_OSC_DI;

/** @brief       逻辑设备LD基本信息表nx_t_ied_ld*/
typedef struct _LD_TB
{
	/** @brief       逻辑设备编号*/
	int n_ld_code;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       设备名称*/
	string str_aliasname;
	/** @brief       LD版本号*/
	string str_ldversion;
	/** @brief       校验码*/
	string str_chkcode;
	/** @brief       功能号*/
	int n_ldfun;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       定值区号表字段链表*/
	LIST_SGZONE v_sgzone;
	/** @brief       定值表字段链表*/
	LIST_SG v_sg;
	/** @brief       软压板字段链表*/
	LIST_STRAP v_softstrap;
	/** @brief       硬压板字段链表*/
	LIST_STRAP v_hardstrap;
	/** @brief       模拟量字段链表*/
	LIST_ANALOG v_analog;
	/** @brief       事件表字段链表*/
	LIST_EVENT v_event;
	/** @brief       告警表字段链表*/
	LIST_ALARM v_alarm;
	/** @brief       故障参数表字段链表*/
	LIST_FAULTTAG v_faulttag;
	/** @brief       IED录波模拟量通道表字段链表*/
	LIST_OSC_AI v_osc_ai;
	/** @brief       IED录波开关量通道表字段链表*/
	LIST_OSC_DI v_osc_di;
	/** @brief       字符备用字段1*/
	string str_strbackup1;
	/** @brief       字符备用字段2*/
	string str_strbackup2;
	/** @brief       字符备用字段3*/
	string str_strbackup3;
	/** @brief       指针备用字段*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_LD_TB()
	{
		n_ld_code		= 1;
		n_ied_obj		= 0;
		n_ldfun			= 0;
		p_backup		= NULL;
	}

}LD_TB;

/** @brief       逻辑设备表字段链表定义*/
typedef list<LD_TB> LIST_LD;

/** @brief       二次设备IED基本信息表nx_t_ied*/
typedef struct _IED_TB
{
	/** @brief       设备编号*/
	int n_obj_id;
	/** @brief       设备名称*/
	string str_aliasname;
	/** @brief       名称缩写*/
	string str_abbrename;
	/** @brief       所属变电站*/
	int n_station_obj;
	/** @brief       关联的一次设备*/
	int n_primequ_obj;
	/** @brief       通讯状态1:正常    0:断开    2:未知*/
	int n_cmmustat;
	/** @brief       通讯状态变化原因*/
	int n_chgreason_obj;
	/** @brief       最新通讯状态更新时间*/
	string str_cmmustattm;
	/** @brief       当前运行状态*/
	TOPRAMODE e_opramode;
	/** @brief       当前运行状态最后更新时间*/
	string str_opramodetm;
	/** @brief       型号*/
	string str_model;
	/** @brief       IED类型*/
	int/*TSECONDDEV_TYPE*/ e_psrtype;
	/** @brief       103地址*/
	int n_outaddr103;
	/** @brief       103组号*/
	int n_outgroup103;
	/** @brief       103条目号*/
	int n_outitem103;
	/** @brief       智能订购 如有多个，中间用$分隔*/
	string str_ecu_orderlist;
	/** @brief       101地址*/
	int n_addr101;
	/** @brief       站内设备地址*/
	int n_addr;
	/** @brief       采集工厂编号*/
	int n_factory_obj;
	/** @brief       A网IP地址*/
	string str_ipaddr_a;
	/** @brief       A网端口*/
	int n_ipport_a;
	/** @brief       B网IP地址*/
	string str_ipaddr_b;
	/** @brief       B网端口*/
	int n_ipport_b;
	/** @brief       装置版本*/
	string str_iedversion;
	/** @brief       校验码*/
	string str_chkcode;
	/** @brief       程序生成时间*/
	string str_programtm;
	/** @brief       cid文件校验码*/
	string str_cidchkcode;
	/** @brief       工作角色*/
	int n_workrole;
	/** @brief       角色顺序*/
	int n_roleseq;
	/** @brief       对象路径*/
	string str_objpathname;
	/** @brief       统一资源标识器*/
	string str_rdfid;
	/** @brief       外部系统编号*/
	string str_ex_rdfid;
	/** @brief       逻辑设备表字段链表*/
	LIST_LD v_ld;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       字符备用3*/
	string str_strbackup3;
	/** @brief       备注*/
	string str_notes;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_IED_TB()
	{
		n_obj_id		= 1;
		n_station_obj	= 0;
		n_primequ_obj	= 0;
		n_cmmustat		= 2;
		n_chgreason_obj	= 0;
		e_opramode		= OPRAMODE_RUN;
		e_psrtype		= LINE_PRO;
		n_outaddr103	= 0;
		n_outgroup103	= 0;
		n_outitem103	= 0;
		n_addr101		= 0;
		n_addr			= 0;
		n_factory_obj	= 0;
		str_ipaddr_a	= "***********";
		n_ipport_a		= 2404;
		str_ipaddr_b	= "***********";
		n_ipport_b		= 2404;
		n_workrole		= 1;
		n_roleseq		= 1;
		p_backup		= NULL;
	}

}IED_TB;

/** @brief        二次设备IED基本信息表字段链表定义*/
typedef list<IED_TB> LIST_IED;

/** @brief       二次设备类型表nx_t_ied_type*/
typedef struct _IED_TYPE_TB
{
	/** @brief       设备类型编号*/
	string str_ied_type;
	/** @brief       生产厂家*/
	string str_manufacturer;
	/** @brief       备注*/
	string str_notes;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       字符备用3*/
	string str_strbackup3;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_IED_TYPE_TB()
	{
		p_backup = NULL;
	}

}IED_TYPE_TB;

/** @brief        二次设备类型表字段链表定义*/
typedef list<IED_TYPE_TB> LIST_IEDTYPE;


/********************************************************************************************************/
/* 信息远传配置表结构定义                                                                               */
/********************************************************************************************************/
/** @brief       对外通信客户端通道配置表nx_t_ecu_channel_cfg*/
typedef struct _ECU_CHANNEL_TB
{
	/** @brief       通道编号*/
	int n_obj_id;
	/** @brief       客户端编号*/
	int n_client_obj;
	/** @brief       通道描述*/
	string str_aliasname;
	/** @brief       链路类型 1：串口 2：tcp 3：udp(broadcast)  4：udp(multicast)*/
	int n_link_type;
	/** @brief       IP地址*/
	string str_ipaddr;
	/** @brief       网络端口*/
	int n_ipport;
	/** @brief       网关*/
	string str_ipnetgate;
	/** @brief       串口号*/
	int n_serial_no;
	/** @brief       波特率*/
	int n_baudrate;
	/** @brief       数据位*/
	int n_databit;
	/** @brief       停止位*/
	int n_stopbit;
	/** @brief       校验位 "n"无校验；"e"偶校验；"o"奇校验*/
	string str_paritybit;
	/** @brief       当前状态*/
	int n_cmmustat;
	/** @brief       发生时间*/
	string str_cmmustattm;
	/** @brief       通讯状态发生原因*/
	int n_reason_obj;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ECU_CHANNEL_TB()
	{
		n_obj_id			= 1;
		n_client_obj		= 0;
		n_link_type			= 2;
		n_ipport			= 0;
		n_serial_no			= 0;
		n_baudrate			= 9600;
		n_databit			= 8;
		n_stopbit			= 1;
		str_paritybit		= "e";
		n_cmmustat			= 2;
		n_reason_obj		= 0;
		p_backup			= NULL;
	}

}ECU_CHANNEL_TB;

/** @brief       对外通信客户端通道配置表字段定义*/
typedef list<ECU_CHANNEL_TB> LIST_CHANNEL;

/** @brief       对外通信客户端不订阅设备配置表nx_t_ecu_dev_not_order_cfg*/
typedef struct _ECU_NOT_ORDER_TB
{
	/** @brief       自动增长的编号*/
	int n_obj_idx;
	/** @brief       客户端编号*/
	int n_client_obj;
	/** @brief       设备类型 1:一次设备  2：IED*/
	int n_eqm_type;
	/** @brief       设备编号*/
	int n_eqm_obj;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ECU_NOT_ORDER_TB()
	{
		n_obj_idx		= 0;
		n_client_obj	= 0;
		n_eqm_type		= 2;
		n_eqm_obj		= 0;
		p_backup		= NULL;
	}

}ECU_NOT_ORDER_TB;

/** @brief       对外通信客户端不订阅设备配置表字段定义*/
typedef list<ECU_NOT_ORDER_TB> LIST_NOT_ORDER;

/** @brief       信息类型定义*/
// enum TINFO_TYPE
// {
// 	INFO_UNKOWN				= 0,/**<  未知 */
// 	INFO_EVENT				= 2001,/**<  IED新动作事件报告 */
// 	INFO_ALARM				= 2002,/**<  IED新告警事件报告 */
// 	INFO_NEW_RCDFILE		= 2003,/**<  IED新录波文件报告 */
// 	INFO_HARD				= 2004,/**<  IED硬压板变位通知报告 */
// 	INFO_SOFT				= 2005,/**<  IED软压板变位通知报告 */
// 	INFO_COMMUSTAT			= 2006,/**<  IED接入通讯状态报告 */
// 	INFO_RUNSTAT			= 3102,/**<  IED运行状态变化报告 */
// 	INFO_SG					= 3103,/**<  IED定值(保护及录波器) */
// 	INFO_SG_CHANGE			= 3104,/**<  IED定值变化报告 */
// 	INFO_SYSALARM			= 3105,/**<  系统告警 */
// 	INFO_SYSCONFIG_CHANGE	= 3456,/**<  系统配置变化通知 */
// 
// };

/** @brief       对外通信客户端信息订制表nx_t_ecu_info_order_cfg*/
typedef struct _ECU_ORDER_TB
{
	/** @brief       自动增长的编号*/
	int n_obj_idx;
	/** @brief       客户端编号*/
	int n_client_obj;
	/** @brief       信息类型*/
	int e_info_type;
	/** @brief       信息类型订制标志 0：不订制 1:订制*/
	bool b_order_flag;
	/** @brief       信息等级订制标志*/
	int n_order_degree;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ECU_ORDER_TB()
	{
		n_obj_idx		= 0;
		n_client_obj	= 0;
		e_info_type		= 0;
		b_order_flag	= 0;
		n_order_degree	= 0;
		p_backup		= NULL;
	}

}ECU_ORDER_TB;

/** @brief       对外通信客户端信息订制表字段链表定义*/
typedef list<ECU_ORDER_TB> LIST_ORDER;

/** @brief       对外通信客户端配置表nx_t_ecu_client_cfg*/
typedef struct _ECU_CLIENT_TB
{
	/** @brief       客户端编号*/
	int n_obj_id;
	/** @brief       客户端描述*/
	string str_aliasname;
	/** @brief       使用的规约编码*/
	int n_pro_obj;
	/** @brief       调试信息是否上送*/
	bool b_send_testinfo; 
	/** @brief       连接断开时信息是否就地保存*/
	bool b_save_indisconn;
	/** @brief       召唤开关量方式 0:从数据库 1：从装置 2：不响应*/
	int n_di_source;
	/** @brief       对时操作0：不予处理; 1:校正主机时间  2:校正装置时间 3:-主机和装置时间同时校正*/
	int n_modify_time;
	/** @brief       远控使能*/
	bool b_ctrlenable;
	/** @brief       接收超时*/
	int n_t_recv;
	/** @brief       发送超时*/
	int n_t_send;
	/** @brief       连接超时*/
	int n_t_t0;
	/** @brief       报告周期*/
	int n_t_report;
	/** @brief       链路控制命令响应超时*/
	int n_t_t1;
	/** @brief       确认超时*/
	int n_t_t2;
	/** @brief       测试帧发送间隔*/
	int n_t_t3;
	/** @brief       日志记录天数*/
	int n_record_day;
	/** @brief       日志记录级别*/
	int n_record_lev;
	/** @brief       整形备用*/
	int n_nbackup;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       字符备用3*/
	string str_strbackup3;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief         通道配置*/
	LIST_CHANNEL      v_ChannelList;
	/** @brief         不订阅设备配置*/
	LIST_NOT_ORDER   v_NotOrderDevList;
	/** @brief         信息类型订阅配置*/
	LIST_ORDER       v_MsgTypeOrderList;

	/** @brief       默认构造函数*/
	_ECU_CLIENT_TB()
	{
		n_obj_id			= 1;
		n_pro_obj			= 0;
		b_send_testinfo		= false;
		b_save_indisconn	= false;
		n_di_source			= 0;
		n_modify_time		= 0;
		b_ctrlenable		= false;
		n_t_recv			= 2;
		n_t_send			= 1;
		n_t_t0				= 30;
		n_t_report			= 30;
		n_t_t1				= 15;
		n_t_t2				= 10;
		n_t_t3				= 20;
		n_record_day		= 15;
		n_record_lev		= 3;
		n_nbackup			= 0;
		p_backup = NULL;
	}

}ECU_CLIENT_TB;

/** @brief       对外通信客户端配置表字段定义*/
typedef list<ECU_CLIENT_TB> LIST_CLIENT;

/** @brief       对外通信监听服务配置表nx_t_ecu_listen_cfg*/
typedef struct _ECU_LISTEN_TB
{
	/** @brief       自动增长的编号*/
	int n_obj_idx;
	/** @brief       监听的IP地址*/
	string str_ls_ip;
	/** @brief       监听端口*/
	int n_ls_port;
	/** @brief       注释说明监听端口的用途*/
	string str_ls_notes;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ECU_LISTEN_TB()
	{
		n_obj_idx		= 0;
		str_ls_ip		= "0.0.0.0";
		n_ls_port		= 2404;
		p_backup		= NULL;
	}

}ECU_LISTEN_TB;

/** @brief       对外通信监听服务配置字段链表定义*/
typedef list<ECU_LISTEN_TB> LIST_LISTEN;

/** @brief       对外通信组标题配置表nx_t_ecu_gtitle_cfg*/
typedef struct _ECU_GTITLE_TB
{
	/** @brief       自动增长的编号*/
	int n_obj_idx;
	/** @brief       组标题组号*/
 	int n_gt_group;
	/** @brief       所属IED*/
	int n_ied_obj;
	/** @brief       所属LD*/
	int n_ld_code;
	/** @brief       组标题名称*/
	string str_gt_name;
 	/** @brief       远控压板, 是否支持远控 1：支持 0：不支持*/
 	bool b_ctrltrap;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_ECU_GTITLE_TB()
	{
		n_obj_idx		= 0;
		n_gt_group		= 0;
		n_ied_obj		= 0;
		n_ld_code		= 0;
		b_ctrltrap		= true;
		p_backup		= NULL;
	}

}ECU_GTITLE_TB;

/** @brief       对外通信组标题配置表字段链表定义*/
typedef list<ECU_GTITLE_TB> LIST_GTITILE;
	

/********************************************************************************************************/
/* 系统共用配置结构定义         																		*/
/********************************************************************************************************/
/** @brief       通讯配置规约表nx_t_protocol_cfg*/
typedef struct _PROTOCOL_CFG_TB
{
	/** @brief       规约编号*/
	int n_obj_id;
	/** @brief       规约名称*/
	string str_aliasname;
	/** @brief       动态库名称, 不带后缀*/
	string str_prodllname;
	/** @brief       规约角色 1:IED接入  2：信息远传*/
	int n_workrole;
	/** @brief       描述*/
	string str_manufacturer;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用1*/
	string str_strbackup2;
	/** @brief       字符备用1*/
	string str_strbackup3;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_PROTOCOL_CFG_TB()
	{
		n_obj_id		= 10000;
		n_workrole		= 1;
		p_backup		= NULL;
	}

}PROTOCOL_CFG_TB;

/** @brief        通讯配置规约表字段链表定义*/
typedef list<PROTOCOL_CFG_TB> LIST_PROTOCOL;

/** @brief       系统基本参数配置表nx_t_basic_cfg*/
typedef struct _BASIC_CFG_TB
{
	/** @brief       自动增长的编号*/
	int n_obj_idx;
	/** @brief       服务器标识*/
	int n_server_id;
	/** @brief       服务器IP地址*/
	string str_server_ip;
	/** @brief       文件共享路径*/
	string str_file_path;
	/** @brief       文件备份路径*/
	string str_filebk_path;
	/** @brief       日志报文根路径*/
	string str_logroot_path;
	/** @brief       日志记录天数*/
	int n_rcd_days;
	/** @brief       日志记录级别*/
	int n_log_level;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用1*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_BASIC_CFG_TB()
	{
		n_obj_idx			= 0;
		n_server_id			= 0;
		str_file_path		= "/var/nxdown/";
		str_logroot_path	= "/var/nxlog/";
		n_rcd_days			= 15;
		n_log_level			= 3;
		p_backup			= NULL;
	}

}BASIC_CFG_TB;

/** @brief        系统基本参数配置表字段链表定义*/
typedef list<BASIC_CFG_TB> LIST_BASIC_CFG;

/** @brief       超时业务功能类型*/
enum TTIMEOUT_TYPE
{
	TMOUT_UNKOWN				= 0,/**<  未知 */
	TMOUT_GET_SG				= 1,/**<  召定值 */
	TMOUT_GET_SGZONE			= 3,/**<  召定值区 */
	TMOUT_GET_SOFT				= 5,/**<  召唤软压板 */
	TMOUT_GET_HARD				= 6,/**<  召唤硬压板 */
	TMOUT_GET_AI				= 9,/**<  召唤模拟量 */
	TMOUT_GET_OSC_LST			= 11,/**<  召录波列表 */
	TMOUT_GET_OSC_FILE			= 13,/**<  召录波文件 */
	TMOUT_GET_FILELST			= 15,/**<  召通用文件列表 */
	TMOUT_GET_FILE				= 17,/**<  召通用文件 */
	TMOUT_GET_TIME				= 21,/**<  召时间 */
	TMOUT_GET_HIS_EVENT			= 23,/**<  召历史事件 */
	TMOUT_GET_COMMU				= 25,/**<  召唤IED通讯状态 */
	TMOUT_SET_SG_CHECK			= 1001,/**<  定值修改预校 */
	TMOUT_SET_SG_EXC			= 1003,/**<  定值修改执行 */
	TMOUT_SET_SGZONE_CHECK		= 1005,/**<  定值区切换预校 */
	TMOUT_SET_SGZONE_EXC		= 1007,/**<  定值区切换执行 */
	TMOUT_SET_SOFT_CHECK		= 1009,/**<  软压板投退预校 */
	TMOUT_SET_SOFT_EXC			= 1011,/**<  软软压板投退执行 */
	TMOUT_IED_RESET				= 1013,/**<  信号复归 */
	TMOUT_IEDREMOTE_TRIP		= 1015,/**<  录波器远方触发 */
	TMOUT_IED_SET_TIME			= 1017,/**<  IED对时 */
	TMOUT_EVENT_RPT				= 2001,/**<  IED新动作事件报告 */
	TMOUT_ALARM_RPT				= 2002,/**<  IED新告警事件报告 */
	TMOUT_OSC_FILE_RPT			= 2003,/**<  IED新录波文件报告 */
	TMOUT_GET_SYSINFO			= 3003,/**<  召系统资源情况 */
	TMOUT_IED_RUNSTATUS_RPT		= 3102,/**<  IED运行状态变化报告 */
	TMOUT_GET_COMMUSTAT_CLIENT	= 3103,/**<  召唤与主站通信状态 */
	TMOUT_SYS_ALARM				= 3105,/**<  系统告警 */
	TMOUT_SYS_CFG_CHANGER		= 3456/**<  系统配置变化通知 */


};

/** @brief       系统业务功能超时配置表nx_t_timeout_cfg*/
typedef struct _TIMEOUT_CFG_TB
{
	/** @brief       功能类型*/
	int e_obj_id;
	/** @brief       超时时间，单位秒*/
	int n_tmout_value;
	/** @brief       注释*/
	string str_notes;
	/** @brief       整形备用字段*/
	int n_nbackup;
	/** @brief       字符备用字段*/
	string str_strbackup;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_TIMEOUT_CFG_TB()
	{
		e_obj_id			= TMOUT_UNKOWN;
		n_tmout_value		= 15;
		n_nbackup			= 0;
		p_backup			= NULL;
	}

}TIMEOUT_CFG_TB;

/** @brief        系统业务功能超时表字段链表定义*/
typedef list<TIMEOUT_CFG_TB> LIST_TIMEOUT;

/** @brief       通讯状态变化原因*/
enum COMMU_CHG_REASON
{
	COMMU_CHG_REASON_BACKUP				= 0,/**<  备用 */
	COMMU_CHG_REASON_NORMAL				= 1,/**<  正常 */
	PHYSICS_ACCESS_2_GETWAY_ABNORMAL	= 2,/**<  到网关的物理通道异常 */
	PHYSICS_ACCESS_2_GETWAY_NORMAL		= 3,/**<  到网关的物理通道正常 */
	PING_IMPASSABLE						= 4,/**<  ping不通 */
	PEER_SIDE_DISCONN_INITIACTIVE		= 5,/**<  物理通道正常,对端主动断开连接 */
	CONN_PEER_SIDE_FAIL					= 6,/**<  物理通道正常，但连接对端失败 */
	HANDSHAKE_MSG_FAIL					= 7,/**<  连接建立后，初始化握手报文失败 */
	SEND_CMD_NO_RESP					= 8,/**<  发送命令无应答 */
	PEER_SIDE_RESP_ERR					= 9,/**<  对端应答信息错误(答非所问) */
	PEER_SIDE_RESP_MSG_ILLEGAL			= 10,/**<  对端应答报文非法(如校验码，报文头、尾或长度不合法等) */
	BIND_SOCKET_FAIL					= 11,/**<  SOCKET绑定失败 */
	CREATE_SOCKET_FAIL					= 12,/**<  创建SOCKET失败; */
	CREATE_PROC_THREAD_FAIL				= 13,/**<  创建进程/线程失败 */
	NOT_RCV_ACK_FROM_PEER               = 14,/**<  在指定时间内没有收到对方的回复确认*/
	PEER_R_S_NO_ERR                     = 15,/**<  对方收发序列号错误*/
	PEER_REQUEST_DISCONNECT             = 16,/**<  对方请求断开*/
	DEV_REJECT_LOGIN					= 100,/**<  装置拒绝登录（可能用户名、密码错误） */
	DEV_REJECT_CONN						= 101,/**<  装置拒绝建立连接（可能连接数超过装置最大个数限制） */
	DEV_DISCONN_INITIACTIVE				= 102/**<  装置主动断开连接（可能序列号混乱、心跳报文收发异常） */
	
};

/** @brief       通讯状态变化原因配置表nx_t_commu_status_chg_reason_cfg*/
typedef struct _CMU_CHG_RSN_TB
{
	/** @brief       通讯状态变化原因编号*/
	int e_obj_id;
	/** @brief       原因对应的描述*/
	string str_aliasname;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_CMU_CHG_RSN_TB()
	{
		e_obj_id		= COMMU_CHG_REASON_BACKUP;
		p_backup		= NULL;
	}

}CMU_CHG_RSN_TB;

/** @brief       通讯状态变化原因表字段链表定义*/
typedef list<CMU_CHG_RSN_TB> LIST_CMU_CHG_RSN;

/** @brief       数据库版本记录表nx_t_db_his_ver*/
typedef struct _DB_HIS_VER_TB
{
	/** @brief       自动增长的编号*/
	int n_obj_idx;
	/** @brief       版本号*/
	string str_dbversion;
	/** @brief       更新内容*/
	string str_chgdesc;
	/** @brief       更新日期*/
	string str_chgtm;
	/** @brief       字符备用1*/
	string str_strbackup1;
	/** @brief       字符备用2*/
	string str_strbackup2;
	/** @brief       指针备用*/
	void* p_backup;
	/** @brief       默认构造函数*/
	_DB_HIS_VER_TB()
	{
		n_obj_idx = 0;
		p_backup	= NULL;
	}

}DB_HIS_VER_TB;

/** @brief       数据库版本记录表字段链表定义*/
typedef list<DB_HIS_VER_TB> LIST_DB_VER;


#endif//NX_MODEL_DEF_H_0000000000000000