/**********************************************************************
* CCsgLogRecordMngr.h        author:flood      date:12/03/2015            
*---------------------------------------------------------------------
*  note:南网日志管理类声明头文件                                   
*  
**********************************************************************/
#ifndef _H_LOG_CSG_RECORD_MNGRH_ 
#define _H_LOG_CSG_RECORD_MNGRH_

#include "CsgLogRecord.h"
//////////////////////////////////////////////////////////////////////////日志管理类
//////////////////////////////////////////////////////////////////////////类声明
//模块编码-三个字节，只可使用英文字母和阿拉伯数字
//日志名格式：最后三个字符为自定义标识符，对第一个字符进行定义，各模块使用时，在初始化接口时要求使用
//RUNLOG_2014_05_07_14_27_34_874_A00.txt
//A**:采集器模块
//B**:对上通讯模块
//C**:配置工具
//D**:客户端界面
//E**:就地系统监视模块
//F**:历史数据管理模块
/**
* @defgroup   CCsgLogRecordMngr 日志记录
* @{
*/
 /**
 * @brief      日志类，以指定模块名创建目录，在该目录下保存日志
 * <AUTHOR>
 * @date       17/08/2013
 * example
 * @code*
 *     CSG_LOG_RECORD_CFG cfg;
 *     cfg.strModulName = "A01";    //采集器日志
 *      CCsgLogRecordMngr  youcsglogMngrObj(&cfg);
 *      CSG_LOG_DATA_UNIT  csg_log_unit;
 *      youcsglogMngrObj.Csg_LogOut(csg_log_unit,CCsgLogRecordMngr::CSG_LOG_TYPE_RUN);
 *      ......
 *      @endcode
 */
class CCsgLogRecordMngr
{
public:
	 /**
	  * @brief         构造函数
	  * @param[in]     LOG_RECORD_CFG* pLogRecord_Cfg  日志配置结构
	  * @param[out]    无
	  * @return        无
	  */
	CCsgLogRecordMngr(CSG_LOG_RECORD_CFG& LogRecord_Cfg);
	~CCsgLogRecordMngr(void);
public:
	/** @brief日志分类枚举 */
	enum CSG_LOG_TYPE
	{
		CSG_LOG_TYPE_RUN		= 0,/**<  运行日志 */
		CSG_LOG_TYPE_OPR		= 1,/**<  操作日志 */
		CSG_LOG_TYPE_MNT		= 2,/**<  维护日志 */
	};
public:
	/**
	  * @brief         写日志
	  * @param[in]     CSG_LOG_DATA_UNIT& csg_log_unit  日志记录单元
	  * @param[in]     unsigned int nCurLogType         日志类别
	  * @param[out]    无
	  * @return        int
	  */
	  int Csg_LogOut(CSG_LOG_DATA_UNIT& csg_log_unit,unsigned int nCurLogType);
	  /**
	  * @brief         设置配置
	  * @param[in]     CSG_LOG_RECORD_CFG& LogRecord_Cfg  日志配置结构
	  * @param[out]    无
	  * @return        无
	  */
	 void Set_Csg_Log_Record_Cfg(CSG_LOG_RECORD_CFG& LogRecord_Cfg);
private:
	  /**
	  * @brief         根据类型获取日志指针
	  * @param[in]     unsigned int nCurLogType
	  * @param[out]    无
	  * @return        CCsgLogRecord*
	  */
	CCsgLogRecord* __Get_Csg_Logger(unsigned int nCurLogType);
private:
	/** @brief      互斥体*/
	CThreadMutualLock     m_mutualLock;
	/** @日志配置项目 */
	CSG_LOG_RECORD_CFG  m_csglogrecord_cfg;
	CCsgLogRecord*  m_plogger_run;//运行日志记录者
	CCsgLogRecord*  m_plogger_opr;//操作日志记录者
	CCsgLogRecord*  m_plogger_mnt;//维护日志记录者
	
};

#endif
