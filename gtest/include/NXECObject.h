/**********************************************************************
* NXECObject.h         author:jjl      date:28/08/2013            
*---------------------------------------------------------------------
*  note: 基类对象头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXECOBJECT_H_ 
#define _H_NXECOBJECT_H_

#include "os_platform_def.h"
#include "os_platform_fun.h"
#include "LogRecord.h"
#include "MyDeque.h"
#include "CsgLogRecordMngr.h"
#include "IniOperate.h"

/** @brief         线程状态枚举*/
enum   EC_THREAD_STATE
{
	T_S_STOP     = 0,      /**<      停止 */
	T_S_START    = 1,      /**<      启动 */
	T_S_SUSPEND  = 2,      /**<      暂停 */
};

/** @brief         线程回调函数指针*/
typedef int      (*PFUNC_THREAD_CALLBACK)(LPVOID pObj,LPVOID pParam);
//mem_fun_ref_t<int,OBJ_TYPE> pMemberFunc;

/** @brief                 线程信息结构*/
//template <typename OBJ_TYPE>
typedef struct _EC_THREAD_INFO
{
	/** @brief         线程句柄*/
	OS_PTHREAD_HANDLE  hThreadHandle;

	/** @brief         线程ID*/
	OS_PTHREAD_ID      nThreadID;

	/** @brief         线程内部任务执行函数（类内部的静态成员函数,非SY_THREAD_FUNCTION类型函数)*/
	PFUNC_THREAD_CALLBACK pCallBackFunc;

	/** @brief         对象本身指针*/
	LPVOID             pSelfObj;   

	/** @brief         任务函数参数(为NULL,表示任务函数不需要参数)*/
	LPVOID             pParam;

	/** @brief         线程描述(格式："功能+函数名称")*/
	string             strThreadDes;

	/** @brief         线程状态*/
	EC_THREAD_STATE    nState;	

	_EC_THREAD_INFO()
	{
		pCallBackFunc     = NULL ;
		hThreadHandle = NULL;
		nThreadID     = 0;
		strThreadDes  = "";
		pSelfObj      = NULL;
		pParam        = NULL;
		nState        = T_S_STOP;
	}

}EC_THREAD_INFO;

/**
* @defgroup   CNXECObject 通用对象基类
* @{
*/
 
/**
 * @brief      实现所有对象的公用操作，如日志处理
 * <AUTHOR>
 * @date       28/08/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXECObject
{
	///////////////////////////////////////////////////////////////构造、析构
protected:

	/**
	* @brief         构造函数
	* @param[in]     CLogRecord * pLogRecord:日志对象指针
	* @param[out]    无
	* @return        无
	*/
	CNXECObject(IN CLogRecord * pLogRecord,IN const char * pClassName);
public:

	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXECObject();

	///////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         记录错误日志
	* @param[in]     const char * cLog:日志内容
	* @return        void
	*/
	virtual void RecordErrorLog(IN const char * cLog);

	/**
	* @brief         记录跟踪日志
	* @param[in]     const char * cLog:日志内容
	* @return        void
	*/
	virtual void RecordTraceLog(IN const char * cLog);

	/**
	* @brief         记录子类调用父类函数执行的错误日志
	* @param[in]     const char * cLog:日志内容
	* @param[in]     const char* cPClass:父类名称
	* @return        void
	*/
	virtual void RcdErrLogWithParentClass(IN const char * cLog, IN const char* cPClass);

	/**
	* @brief         记录子类调用父类函数执行的跟踪日志
	* @param[in]     const char * cLog:日志内容
	* @param[in]     const char* cPClass:父类名称
	* @return        void
	*/
	virtual void RcdTrcLogWithParentClass(IN const char * cLog, IN const char* cPClass);

	/**
	* @brief         设置日志对象
	* @param[in]     CLogRecord * pLogRecord:日志对象
	* @return        void
	*/
	virtual void SetLogRecord(IN CLogRecord * pLogRecord);

	 /*
     *  @brief   	设置运行时类的对象名称
     *  @param[IN]  const char * chObjName:运行时的实例对象名称 		
     *  @return 	bool true-成功 false-失败
     */
	 virtual bool SetRunTimeObjName(IN const char * chObjName);

	 /**
	 * @brief					
	 * @param[in]  
	 * @return		0-执行成功；其他-执行失败
	 * @note 
	 **/
	 virtual bool CsgLogOutRun(IN CSG_LOG_DATA_UNIT& csg_log_unit);

	 /**
	 * @brief					
	 * @param[in]  
	 * @return		0-执行成功；其他-执行失败
	 * @note 
	 **/
	 virtual bool CsgLogOutOpr(IN CSG_LOG_DATA_UNIT& csg_log_unit);

	 /**
	 * @brief					
	 * @param[in]  
	 * @return		0-执行成功；其他-执行失败
	 * @note 
	 **/
	 virtual bool CsgLogOutMnt(IN CSG_LOG_DATA_UNIT& csg_log_unit);

	///////////////////////////////////////////////////////////////保护方法

protected:

	/**
	* @brief         增加线程参数信息
	* @param[in]     EC_THREAD_INFO * ThreadInfo:线程参数结构
	* @return        bool true-成功 false-失败
	*/
	virtual bool  AddThreadInfo( EC_THREAD_INFO*  pThreadInfo);

	/**
	* @brief         启动所有线程
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/
	virtual bool  StartAllThread();

	/**
	* @brief         启动参数指定的线程,
	* @param[in]     EC_THREAD_INFO * pThreadInfo:线程结构
	* @return        bool true-成功 false-失败
	*/
	virtual bool StartOneThread(IN EC_THREAD_INFO * pThreadInfo);
	/**
	* @brief         停止所有线程
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/
	virtual bool  StopAllThread();

	/**
	* @brief         打开日志记录对象
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-正常 其它：失败
	*/
	int             _OpenLogRecord();

	/**
	* @brief         关闭日志记录对象
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-正常 其它：失败
	*/
	int             _CloseLogRecord();

	/**
	* @brief         设置日志存放根路径
	* @param[in]     iconst char * pszRootPath 日志路径
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool            _SetLogRootPath(IN const char * pszRootPath);

	/**
	* @brief         设置日志记录的模块名称
	* @param[in]     const char * c_theName 日志对象名称
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool            _SetLogModuleName(IN const char * c_ModuleName);

	/**
	* @brief         设置日志级别
	* @param[in]     int nLogRecordLevel 日志级别 0,1,2,3,见枚举
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool            _SetLogLevel(IN unsigned int nLevel);

	 /*
     *  @brief   	设置日志保存天数
     *  @param[in]  unsigned int nDays  日志保存天数
     *  @return 	bool true-成功 false-失败
     */
	bool            _SetLogRecordDays(IN unsigned int nDays);

	/*
     *  @brief   	设置单个日志文件最大多少Mb
     *  @param[in]  unsigned int nMaxSize  单位:MB
     *  @return 	bool true-成功 false-失败
     */
	bool            _SetLogFileMaxMb(IN long nMaxSize);

	/*
     *  @brief   	设置是否打印到屏幕
     *  @param[In]  true:打印 false:不打印
     *  @return 	bool true-成功 false-失败
     */
	bool            _SetLogPrint2Screen(IN bool bPrint2Screen);

	/*
     *  @brief   	获取是否打印到屏幕
     *  @param[IN]  无 		
     *  @return 	bool true-打印 false-不打印
     */
	 bool           _IsPrintLog2Screen();

	/**
	* @brief         设置日志记录的类名称
	* @param[in]     const char * cClassName 类名称
	* @param[out]    无
	* @return        void
	*/
	 void           _SetLogClassName(IN const char * cClassName);

	//////////////////////////////////////////////////////////////私有方法
private:

	/*
     *  @brief   	获得运行时对象的类名称(需要编译器打开RTTI选项)
     *  @param 		无
     *  @return 	bool true-成功 false-失败
     */
	bool            __GetRTTIClassName();

	/*
     *  @brief      线程函数
     *  @param 		LPVOID * pParam:线程参数
     *  @return 	bool true-成功 false-失败
     */
	static SY_THREAD_FUNCTION __ProxyThreadPro(LPVOID * pParam);

	/*
     *  @brief      格式化日志内容
     *  @param[in] 	const char * cLog:原内容
	 *  @param[out] string strLog;格式化后日志内容
     *  @return 	void
     */
	void __FormatLogString(IN const char * cLog,OUT string & strLog);

	///////////////////////////////////////////////////////////////保护成员
protected:

	/** @brief             日志对象指针*/
	CLogRecord *           m_pLogRecord;
public:
	/** @brief             南网扩展规约日志对象指针*/
	CCsgLogRecordMngr*     m_CsgLogRecord;

	/** @brief         Csg日志类配置结构*/
	CSG_LOG_RECORD_CFG m_CsgLogRecordCfg;
	///////////////////////////////////////////////////////////////私有成员
private:

	/** @brief         日志记录根路径*/
	string             m_strRootPath;

	/** @brief         模块名称*/
	string             m_strModelName;

	/** @brief         日志记录天数*/
	int                m_nRecordDays;

	/** @brief         日志记录级别*/
	int                m_nRecordLevel;

	/** @brief         日志是否输出到屏幕*/
	bool               m_bPrintToScreen;

	/** @brief         日志文件最大容量(单位:Mb)*/
	int                m_nMaxSize;

	/** @brief         当前的类名*/
	string             m_strCurClassName;

	/** @brief         运行时对象名描述*/
	string             m_strRunTimeObjName;

	/** @brief         线程信息队列*/
	CMyDeque< EC_THREAD_INFO *> m_ThreadDeque;

	

};

/** @} */ //OVER
#endif