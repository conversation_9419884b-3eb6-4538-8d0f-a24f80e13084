/**********************************************************************
* ec_pro_common_def.h         author:jjl      date:17/10/2013            
*---------------------------------------------------------------------
*  note:规约公用定义头文件                                                              
*  
**********************************************************************/

#ifndef _H_EC_PRO_COMMON_DEF_H_ 
#define _H_EC_PRO_COMMON_DEF_H_

#include "ec_common_def.h"

#pragma warning(once : 4244)  

///////////////////////////////////////////////////////////////////类型定义
/** @brief         报文帧数据*/
typedef vector<u_int8> PRO_FRAME_DATA;
typedef vector<PRO_FRAME_DATA> PRO_FRAME_DATA_LIST;

/** @brief         规约转换类型*/
typedef enum _EC_PRO_CVT_TYPE
{
	CVT_UNKNOWN           = 0,        /**<     未知类型*/
	CVT_TO_EVENT          = 1,        /**<     转换为NX事件消息*/
	CVT_TO_CALL           = 2,        /**<     转换为NX通用召唤消息*/
	CVT_FROM_LOCAL        = 3,        /**<     直接从本地磁盘或数据库获取结果*/
	CVT_TO_CTRL           = 4,        /**<     转换为NX控制类消息*/
}EC_PRO_CVT_TYPE;


/** @brief         报文帧类型*/
typedef enum _EC_FRAME_TYPE
{
	FM_TYPE_UNKNOWN  = 0,   /**<     无效帧 */
	FM_TYPE_APPDATA  = 1,   /**<     应用数据帧 */
	FM_TYPE_LINKCTL  = 2,   /**<     传输控制帧(如测试帧、启停等非传输应用数据的短报文) */
}EC_FRAME_TYPE;


///////////////////////////////////////////////////////////////////常量定义
/** @brief         无效数据*/
const int16 EC_PRO_INVALID_VALUE = -1;

/** @brief         规约转换失败*/
const int EC_PRO_CVT_FAIL = -1;

/** @brief         不支持转换*/
const int EC_PRO_CVT_NOSUPPORT = -2;

/** @brief         转换成功*/
const int EC_PRO_CVT_SUCCESS = 0;

////////////////////////////////////////////////////////////////////结构定义

/** @brief         规约帧数据结构*/
typedef struct _PRO_SRC_FRAME
{
	/** @brief         帧编号*/
	UINT            nFrameID;
	/** @brief         帧类型*/
	EC_FRAME_TYPE   eFrameType;
	/** @brief         发送/接收时间*/
	time_t          nRSTime;
	/** @brief         完整帧数据*/
	PRO_FRAME_DATA  vSrcData;

	_PRO_SRC_FRAME()
	{
		nFrameID   = EC_PRO_INVALID_VALUE;
		eFrameType = FM_TYPE_UNKNOWN;
	}

}PRO_SRC_FRAME;

/** @brief         原始帧队列*/
typedef deque<PRO_SRC_FRAME> PRO_SRC_FRAME_DEQUE;


/** @brief         规约报文帧体信息*/
typedef struct _PRO_FRAME_BODY
{
	/** @brief         帧编号*/
	UINT nFrameID;
	/** @brief         类型标识*/
	int16 nType;
	/** @brief         可变结构限定词*/
	int16 nVsq;
	/** @brief         传输原因*/
	int16 nCot;
	/** @brief         厂站地址*/
	int16 nSubstationAdd;
	/** @brief         设备地址*/
	int16 nAddr;
	/** @brief         设备cpu号*/
	int16 nCpu;
	/** @brief         设备定值区号*/
	int16 nZone;
	/** @brief         功能类型*/
	int16 nFun;
	/** @brief         信息序号*/
	int16 nInf;
	/** @brief         返回信息标识符*/
	int16 nRii;  
	/** @brief         通用分类数据集数目*/
	int16 nNog;
	/** @brief         通用分类组号*/
	int16 nGroup;
	/** @brief         通用分类条目号*/
	int16 nItem;
	/** @brief         通用分类KOD*/
	int16 nKod;
	/** @brief         是否最后一帧*/
	bool bLast;
	/** @brief         备用1*/  
	int16 nRes1; 
	/** @brief         备用2*/
	int    nRes2;
	/** @brief         变长数据（对于IEC103规约可变部分统一为从报文体第1个字节->信息序号*/
	PRO_FRAME_DATA  vVarData;

	_PRO_FRAME_BODY()
	{
		nFrameID = EC_PRO_INVALID_VALUE;
		nType    = EC_PRO_INVALID_VALUE;
		nVsq     = EC_PRO_INVALID_VALUE;
		nCot     = EC_PRO_INVALID_VALUE;
		nSubstationAdd = EC_PRO_INVALID_VALUE;
		nAddr    = EC_PRO_INVALID_VALUE;
		nCpu     = EC_PRO_INVALID_VALUE;
		nZone    = EC_PRO_INVALID_VALUE;
		nFun     = EC_PRO_INVALID_VALUE;
		nInf     = EC_PRO_INVALID_VALUE;
		nRii     = EC_PRO_INVALID_VALUE;
		nNog     = EC_PRO_INVALID_VALUE;
		nGroup   = EC_PRO_INVALID_VALUE;
		nItem    = EC_PRO_INVALID_VALUE;
		nKod     = EC_PRO_INVALID_VALUE;
		bLast    = true;
		nRes1    = EC_PRO_INVALID_VALUE;
		nRes2    = EC_PRO_INVALID_VALUE;
	}

}PRO_FRAME_BODY;

/** @brief         帧体列表*/
typedef list <PRO_FRAME_BODY> PRO_FRAME_BODY_LIST;

#endif  // _H_EC_PRO_COMMON_DEF_H_