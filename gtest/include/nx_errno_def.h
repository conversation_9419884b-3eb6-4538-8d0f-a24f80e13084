/********************************************************************************
模块名       : nx系统错误编码
文件名       : nx_errno_def.h
文件实现功能 : 系统内出错信息的统一定义与处理 
--------------------------------------------------------------------------------
修改记录 : 
日 期        版本     修改人              修改内容
2013/08/16   1.0.0    sl                  创建

 *******************************************************************************/ 

#ifndef _H_NX_ERRNO_DEF_H_ 
#define _H_NX_ERRNO_DEF_H_

/************************************************************************
*各部分错误编码范围定义
************************************************************************/
/** @brief  基准编号起始结束范围*/
#define  NX_ERRNO_BASE         0xFFFF
#define  NX_ERRNO_MAX		      (NX_ERRNO_BASE + 0x9FFF)

/** @brief  系统公用错误编号范围*/
#define NX_ERRNO_COMMON_BASE  (NX_ERRNO_BASE+0x0000)
#define NX_ERRNO_COMMON_MAX   (NX_ERRNO_BASE+0x0FFF)

/** @brief  数据库接口错误编号范围*/
#define NX_ERRNO_DBM_BASE     (NX_ERRNO_BASE+0x1000)
#define NX_ERRNO_DBM_MAX      (NX_ERRNO_DBM_BASE+0x0FFF)

/** @brief  消息总线错误编号范围*/
#define NX_ERRNO_MB_BASE      (NX_ERRNO_BASE+0x2000)
#define NX_ERRNO_MB_MAX       (NX_ERRNO_MB_BASE+0x0FFF)

/** @brief  采集工厂错误编号范围*/
#define NX_ERRNO_GF_BASE      (NX_ERRNO_BASE+0x3000)
#define NX_ERRNO_GF_MAX       (NX_ERRNO_GF_BASE+0x0FFF)

/** @brief  用户界面错误编号范围*/
#define NX_ERRNO_SF_BASE      (NX_ERRNO_BASE+0x4000)
#define NX_ERRNO_SF_MAX       (NX_ERRNO_SF_BASE+0x0FFF)

/** @brief  对外通信错误编号范围*/
#define NX_ERRNO_EC_BASE      (NX_ERRNO_BASE+0x5000)
#define NX_ERRNO_EC_MAX       (NX_ERRNO_EC_BASE+0x0FFF)

/** @brief  配置工具错误编号范围*/
#define NX_ERRNO_CT_BASE      (NX_ERRNO_BASE+0x6000)
#define NX_ERRNO_CT_MAX       (NX_ERRNO_CT_BASE+0x0FFF)

/** @brief  数据管理错误编号范围*/
#define NX_ERRNO_HM_BASE      (NX_ERRNO_BASE+0x7000)
#define NX_ERRNO_HM_MAX       (NX_ERRNO_HM_BASE+0x0FFF)

/** @brief  资源监视错误编号范围*/
#define NX_ERRNO_SM_BASE      (NX_ERRNO_BASE+0x8000)
#define NX_ERRNO_SM_MAX       (NX_ERRNO_SM_BASE+0x0FFF)

/***********************************************************************
*各部分错误编码及详细含义定义
************************************************************************/

/** @brief  NX_ERRNO_COMMON  系统通用错误编号--------------------------*/
/** @brief 未知错误*/
#define  COMMONERR_UNEXCEPTED             (NX_ERRNO_COMMON_BASE+0)
/** @brief 内存分配失败*/
#define  COMMONERR_NEW_MEM_FAILED         (NX_ERRNO_COMMON_BASE+1)
/** @brief 动态库加载失败*/
#define  COMMONERR_LOAD_LIB_FAILED        (NX_ERRNO_COMMON_BASE+2)
/** @brief 获得函数地址失败*/
#define  COMMONERR_GET_FUNADDR_FAILED     (NX_ERRNO_COMMON_BASE+3)
/** @brief 运行中异常*/
#define  COMMONERR_RUN_EXCEPTION          (NX_ERRNO_COMMON_BASE+4)
/** @brief 无效地址或指针*/
#define COMMONERR_INVALID_PARAMETER       (NX_ERRNO_COMMON_BASE+5)
/** @brief 启动线程失败*/
#define COMMONERR_CREATE_THREAD_FAILED    (NX_ERRNO_COMMON_BASE+6)
/** @brief 结束线程失败*/
#define COMMONERR_END_THREAD_FAILED       (NX_ERRNO_COMMON_BASE+7)
/** @brief 登陆验证失败*/
#define COMMONERR_VERIFY_FAILED           (NX_ERRNO_COMMON_BASE+8)


//extern char* ErrMsg_Common[];
// ={
// 	      "unexpected common error",
// 	      "mem initialize failed",
// 	      "load lib failed",
// 	      "get function address failed",
// 	      "unusual run",
// 	      "invalid address or point",
// 	      "create thread failed",
// 	      "terminate thread failed",
// 	      "verify failed",
// 	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_DBM  数据库接口错误-------------------------------*/
/** @brief 未知错误*/
#define  DBMERR_UNEXCEPTED           (NX_ERRNO_DBM_BASE+0)

//extern char* ErrMsg_Dbm[];
// ={
// 	     "unexpected dbm error",	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_MB  消息总线错误----------------------------------*/
/** @brief 未知错误*/
#define  MBERR_MB_UNEXCEPTED          (NX_ERRNO_MB_BASE+0)

//extern char* ErrMsg_Mb[];
// ={
// 	     "unexpected mb error",	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_GF  采集工厂错误----------------------------------*/
/** @brief 未知错误*/
#define  GFERR_GF_UNEXCEPTED          (NX_ERRNO_GF_BASE+0)

//extern char* ErrMsg_Gf[];
// ={
// 	     "unexpected gf error",	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_SF  采集工厂错误----------------------------------*/
/** @brief 未知错误*/
#define  SFERR_SF_UNEXCEPTED          (NX_ERRNO_SF_BASE+0)

//extern char* ErrMsg_Sf[];
// ={
// 	     "unexpected sf error",	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_EC 外部通信---------------------------------------*/
/** @brief 未知错误*/
#define  ECERR_EC_UNEXCEPTED           (NX_ERRNO_EC_BASE+0)

/** @brief         与总线交换数据业务库启动失败*/
#define  ECERR_BUS_SWAP_START_FILED    (NX_ERRNO_EC_BASE+1)

/** @brief         通信结点管理服务启动失败*/
#define  ECERR_NODE_MGR_START_FAILED   (NX_ERRNO_EC_BASE+2)

/** @brief         网络监听服务启动失败*/
#define  ECERR_NET_LISTEN_START_FAILED (NX_ERRNO_EC_BASE+3)

/** @brief         读取数据库模型失败*/
#define  ECERR_READ_EC_MODEL_FAILED (NX_ERRNO_EC_BASE+4)

//extern char* ErrMsg_Ec[];
// ={
// 	     "unexpected ec error",	
// 	
// };
/*----------------------------------------------------------------------*/



/** @brief  NX_ERRNO_CT 配置工具---------------------------------------*/
/** @brief 未知错误*/
#define  CTERR_CT_UNEXCEPTED          (NX_ERRNO_CT_BASE+0)

//extern char* ErrMsg_Ct[];
// ={
// 	     "unexpected ct error",	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_HM 数据管理---------------------------------------*/
/** @brief 未知错误*/
#define  HMERR_HM_UNEXCEPTED          (NX_ERRNO_HM_BASE+0)

//extern char* ErrMsg_Hm[];
// ={
// 	     "unexpected hm error",	
// 	
// };
/*----------------------------------------------------------------------*/


/** @brief  NX_ERRNO_SM 系统监视---------------------------------------*/
/** @brief 未知错误*/
#define  SMERR_SM_UNEXCEPTED           (NX_ERRNO_SM_BASE+0)
#define  SMERR_GET_DISKSZIE_FAILED     (NX_ERRNO_SM_BASE+1)
#define  SMERR_QUERY_TABLE_FAILED      (NX_ERRNO_SM_BASE+2)

//extern char* ErrMsg_Sm[];
// ={
// 	"unexpected sm error",	
// 	"get disk's size failed",
// 	"query table's record failed",
// };
/*----------------------------------------------------------------------*/

//extern char* GetErrnoMsg(int g_nErrno);

#endif 