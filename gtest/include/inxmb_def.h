/**********************************************************************
* inxmb_def.h         author:flood      date:19/08/2013            
*---------------------------------------------------------------------
*  note:数据总线各种结构定义                                                           
*  
**********************************************************************/
#ifndef _H_FLD_INXMB_STRUCT_DEF_H_ 
#define _H_FLD_INXMB_STRUCT_DEF_H_


/**@brief 应用模型的版本管理记录在此*/
/*
用宏定义方式进行管理，模型每做一次修改，需要在此进行版本管理
宏名字:APP_NXMB_MODEL_VERSION
宏格式:版本号_修订日期 
*/
/*
修改人:      YZH
修改日期:    2014.01.08
修改内容:    1.第一次出版
最新版本号:  Ver1.0.0
*/
#define  APP_NXMB_MODEL_VERSION  "Ver1.0.0_2014.01.08"


/**@brief 平台相关宏定义及结构定义*/
#include "os_platform_def.h"

////////////////////////////////////////////////////////////////////////////MB通用宏定义
#define  MB_DST_NODE_BROADCAST  "broadcast"   /** @目的地址为广播*/
#define  MB_SUBOBJLIST_ALL      "all"         /** @全部子对象*/


#define  MB_NODE_LOGSTATE_LOGIN    1          /** @节点当前登录状态为已登陆状态*/
#define  MB_NODE_LOGSTATE_LOGOUT   2          /** @节点当前登录状态为未登陆状态*/

#define  MB_SEND_REASON_ASK_SEND    0         /** @传输原因为召唤上送*/
#define  MB_SEND_REASON_AUTO_SEND   1         /** @传输原因为自动上送*/

#define  NX_IED_EVENTTYPE_EVENT        0      /** @ 动作事件 */
#define  NX_IED_EVENTTYPE_FLTTAG       1      /** @ 故障参数 */

#define  NX_IED_DATA_QULITY_OPRATION            1 /** @ 数据品质为运行 */
#define  NX_IED_DATA_QULITY_TEST                2 /** @ 数据品质为检修 */
#define  NX_IED_DATA_QULITY_INVALID             3 /** @ 数据品质为无效 */

#define  MB_COMMAND_OPRATION_RESULT_SUCCEED     0  /** @操作结果    0:成功  1：操作失败  2：操作超时 3:不支持*/
#define  MB_COMMAND_OPRATION_RESULT_FAIL        1  /** @操作结果    0:成功  1：操作失败  2：操作超时 3:不支持*/
#define  MB_COMMAND_OPRATION_RESULT_TIMEOUT     2  /** @操作结果    0:成功  1：操作失败  2：操作超时 3:不支持*/
#define  MB_COMMAND_OPRATION_RESULT_UNSUPPORT   3 /** @操作结果    0:成功  1：操作失败  2：操作超时 3:不支持*/

////////////////////////////////////////////////////////////////////////////MSG_TYPE宏定义
/** @ 基本类分组*/
#define  MB_BASE_OPREATION_COMFIRM    10000    /** @操作请求收到确认回复*/
#define  MB_BASE_OPREATION_UNSUPPORT  10001    /** @操作不支持回复*/
#define  MB_BASE_OPREATION_FAIL    	  10002    /** @操作失败回复*/
#define  MB_BASE_OPREATION_CANCEL     9999     /** @对已下发的操作请求取消*/

/** @ IED信息召唤类分组*/
#define  NX_IED_CALL_SG_ASK            1     /** @召唤定值请求*/
#define  NX_IED_CALL_SG_REP            2     /** @召唤定值回复*/
#define  NX_IED_CALL_SGZONE_ASK        3     /** @召唤当前定值区号请求*/
#define  NX_IED_CALL_SGZONE_REP        4     /** @召唤当前定值区号回复*/
#define  NX_IED_CALL_SOFTSTRAP_ASK     5     /** @召唤软压板请求*/
#define  NX_IED_CALL_SOFTSTRAP_REP     6     /** @召唤软压板回复*/
#define  NX_IED_CALL_HARDSTRAP_ASK     7     /** @召唤硬压板请求*/
#define  NX_IED_CALL_HARDSTRAP_REP     8     /** @召唤硬压板回复*/
#define  NX_IED_CALL_ANALOG_ASK        9     /** @召唤模拟量请求*/
#define  NX_IED_CALL_ANALOG_REP        10    /** @召唤模拟量回复*/
#define  NX_IED_CALL_OSCLIST_ASK       11    /** @召唤录波文件列表请求*/
#define  NX_IED_CALL_OSCLIST_REP       12    /** @召唤录波文件列表回复*/
#define  NX_IED_CALL_OSCFILE_ASK       13    /** @召唤录波文件请求*/
#define  NX_IED_CALL_OSCFILE_REP       14    /** @召唤录波文件回复*/
#define  NX_IED_CALL_IEDFILELIST_ASK   15    /** @召唤通用文件列表请求*/
#define  NX_IED_CALL_IEDFILELIST_REP   16    /** @召唤通用文件列表回复*/
#define  NX_IED_CALL_IEDFILE_ASK       17    /** @召唤通用文件请求*/
#define  NX_IED_CALL_IED_FILE_REP      18    /** @召唤通用文件回复*/
#define  NX_IED_CALL_IEDPARAM_ASK      19    /** @召唤参数请求*/
#define  NX_IED_CALL_IEDPARAM_REP      20    /** @召唤参数回复*/
#define  NX_IED_CALL_IEDTIME_ASK       21    /** @召唤时间请求*/
#define  NX_IED_CALL_IEDTIME_REP       22    /** @召唤时间回复*/
#define  NX_IED_CALL_HISEVENT_ASK      23    /** @召唤历史事件请求*/
#define  NX_IED_CALL_HISEVENT_REP      24    /** @召唤历史事件回复*/
#define  NX_IED_CALL_CMMU_ASK          25    /** @召唤IED通讯状态请求*/
#define  NX_IED_CALL_CMMU_REP          26    /** @召唤IED通讯状态回复*/

//update by yys 20200220
#define  NX_IED_CALL_ROBOTCHECK_ASK          31    /** @召唤机器人巡视请求*/
#define  NX_IED_CALL_ROBOTCHECK_REP          32   /** @召唤机器人巡视回复*/
#define  NX_IED_CALL_ROBOTCHECK_REPORT		  2008	/** @机器人巡视完成报告     因为后面提交的日志服务使用了33，和2800保持一致，调整该报告ID为2008*/

#define  NX_IED_CALL_ALARM_REP         28   /** @召唤IED告警状态回复    add by yzh 20170926**/
#define  NX_IED_CALL_ACTION_REP        30    /** @召唤IED动作状态回复   add by yzh 20170926**/

//update by yys 20241206
#define  NX_IED_CALL_CFG_ASK          50    /** @召唤装置点表请求*/
#define  NX_IED_CALL_CFG_REP          51   /** @召唤装置点表回复*/


/** @ 召唤类分组—61850相关*/
#define  NX_IED_CALL_61850SRV_READ_ASK   101     /** @61850读值服务请求*/
#define  NX_IED_CALL_61850SRV_READ_REP   102     /** @61850读值服务回复*/
#define  NX_IED_CALL_61850SRV_INIT_ASK   103     /** @61850初始化请求*/
#define  NX_IED_CALL_61850SRV_INIT_REP   104     /** @61850初始化回复*/
#define  NX_IED_CALL_61850SRV_CID_ASK    105     /** @召唤IED CID文件请求*/
#define  NX_IED_CALL_61850SRV_CID_REP    106     /** @召唤IED CID文件回复*/
#define  NX_IED_CALL_61850CLI_REPORT     110     /** @61850客户端主动上送参引和值  add by yzh 20170926*/

/** @IED控制类分组*/
#define  NX_IED_CTRL_SG_CHECK_ASK         1001    /** @定值修改预校请求*/
#define  NX_IED_CTRL_SG_CHECK_REP         1002    /** @定值修改预校回复*/
#define  NX_IED_CTRL_SG_EXC_ASK           1003    /** @定值修改执行请求*/
#define  NX_IED_CTRL_SG_EXC_REP           1004    /** @定值修改执行回复*/
#define  NX_IED_CTRL_SGZONE_CHECK_ASK     1005    /** @定值区切换预校请求*/
#define  NX_IED_CTRL_SGZONE_CHECK_REP     1006    /** @定值区切换预校回复*/
#define  NX_IED_CTRL_SGZONE_EXC_ASK       1007    /** @定值区切换执行请求*/
#define  NX_IED_CTRL_SGZONE_EXC_REP       1008    /** @定值区切换执行回复*/
#define  NX_IED_CTRL_SOFTSTRAP_CHECK_ASK  1009    /** @软压板投退预校请求*/
#define  NX_IED_CTRL_SOFTSTRAP_CHECK_REP  1010    /** @软压板投退预校回复*/
#define  NX_IED_CTRL_SOFTSTRAP_EXC_ASK    1011    /** @软压板投退执行请求*/
#define  NX_IED_CTRL_SOFTSTRAP_EXC_REP    1012    /** @软压板投退执行回复*/
#define  NX_IED_CTRL_IEDTRIP_REST_ASK     1013    /** @信号复归请求*/
#define  NX_IED_CTRL_IEDTRIP_REST_REP     1014    /** @信号复归回复*/
#define  NX_IED_CTRL_IEDREMOTE_TRIP_ASK   1015    /** @录波触发请求*/
#define  NX_IED_CTRL_IEDREMOTE_TRIP_REP   1016    /** @录波触发回复*/
#define  NX_IED_CTRL_SET_TIME_ASK         1017    /** @IED对时下发请求*/
#define  NX_IED_CTRL_SET_TIME_REP         1018    /** @IED对时下发回复*/

#define  NX_IED_CTRL_61850SRV_WRITE_ASK   1101    /** @61850写值服务请求*/
#define  NX_IED_CTRL_61850SRV_WRITE_REP   1102    /** @61850写值服务回复*/

/************************************2018-4-12增加硬压板遥控预校和执行消息类型begin*************************************************************/
#define  NX_IED_CTRL_HARDSTRAP_CHECK_ASK  1201  /** @硬压板投退预校请求*/
#define  NX_IED_CTRL_HARDSTRAP_CHECK_REP  1202  /** @硬压板投退预校回复*/
#define  NX_IED_CTRL_HARDSTRAP_EXC_ASK    1203    /** @硬压板投退执行请求*/
#define  NX_IED_CTRL_HARDSTRAP_EXC_REP    1204    /** @硬压板投退执行回复*/
/************************************2018-4-12增加硬压板遥控预校和执行消息类型end*************************************************************/

/**@IED事件类分组*/
#define  NX_IED_EVENT_EVENT_REPORT        2001  /** @IED新动作事件报告*/
#define  NX_IED_EVENT_ALARM_REPORT        2002  /** @IED新告警事件报告*/
#define  NX_IED_EVENT_OSCFILE_REPORT      2003  /** @IED新录波文件报告*/
#define  NX_IED_EVENT_HARDTRAP_REPORT     2004  /** @IED硬压板变位通知报告*/
#define  NX_IED_EVENT_SOFTTRAP_REPORT     2005  /** @IED 软压板变位通知报告*/
#define  NX_IED_EVENT_COMMU_REPORT        2006  /** @IED接入通讯状态报告*/
#define  NX_IED_EVENT_CID_REPORT          2100  /** @IED CID文件生成报告*/

/** @系统类分组*/
#define  NX_SYS_CALL_ECUSTATUS_ASK       3001      /** @召唤信息远传模块通讯状态请求*/
#define  NX_SYS_CALL_ECUSTATUS_REP       3002      /** @召唤信息远传模块通讯状态回复*/
#define  NX_SYS_CALL_SYSINFO_ASK         3003      /** @召唤系统资源信息请求*/
#define  NX_SYS_CALL_SYSINFO_REP         3004      /** @召唤系统资源信息回复*/

#define  NX_SYS_EVENT_ECU_COMMU_REPORT             3101  /** @远传模块通讯状态报告*/
#define  NX_SYS_EVENT_IED_RUNSTATUS_REPORT         3102  /** @IED运行状态变化报告*/
#define  NX_SYS_EVENT_IED_SG_CHG_REPORT            3103  /** @IED定值变化报告*/
#define  NX_SYS_EVENT_IED_SGZONE_CHG_REPORT        3104  /** @IED定值区变化报告 */
#define  NX_SYS_EVENT_SYS_INFO_REPORT              3105  /** @系统资源监视报告*/
#define  NX_SYS_EVENT_IED_ANACHG_REPORT            3106  /** @IED输入模拟量越限报告*/

#define  NX_SYS_EVENT_IEDS_ANALOG_UNIFORM_REPORT   3107  /** @IED双套装置模拟量不一致报告  add by yzh 20170331*/
#define  NX_SYS_EVENT_IED_ANALOG_AD_DIFFER_REPORT  3108  /** @IED装置模拟量双AD不一致报告  add by yzh 20170331*/
#define  NX_SYS_EVENT_IED_ANALOG_HOMOLOGY_REPORT   3109  /** @IED装置同源数据不一致报告    add by yzh 20170330*/

#define  NX_SYS_EVENT_IED_RUNSTATUS_DIFFER_REPORT  3110  /** @IED运行状态不一致报告        add by yzh 20170306*/
#define  NX_SYS_EVENT_IED_STLIGHTER_REPORT         3111  /** @IED运行状态界面总指示报告    add by yzh 20170306*/
#define  NX_SYS_EVENT_IED_PICRC_DIFFER_REPORT      3112  /** @IED 过程层CRC校验码异常报告  add by yzh 20170317*/
#define  NX_SYS_EVENT_STATION_STLIGHTER_REPORT     3113  /** @全站指示灯状态报告            add by yzh 20170522*/
#define  NX_SYS_EVENT_IED_CHECK_REPORT			   		 3117  /** @IED 校核结果报告*/

#define  NX_SYS_EVENT_GRID_FAULT_REPORT            3200  /** @电网故障报告 add by yzh 20170306*/
#define  NX_SYS_EVENT_CONFIG_ALARM                 3456  /** @系统配置变化通知*/

#define  NX_SYS_CTRL_NX_RESOURCE_RESTART_ASK  3900 /** @ 系统资源远程重启命令*/
#define  NX_SYS_CTRL_NX_RESOURCE_RESTART_REP  3901 /** @ 系统资源远程重启回复*/

#define  NX_SYS_TURPAN_NODE_CFGCHG_RPT			3300 /** @ 传感器描述变化通知  2021-12-07 根据徐凯杰提出的要求增加*/

#define  NX_SYS_EVENT_IED_ENABLE_FAIL_REPORT			3400 /** @ IED使能失败通知*/

/** @过程层信息采集与分析报告*/

#define  UN_IED_PRO_COMMU_REPORT            4000  /** @ IED过程层通讯链路状态报告*/
#define  UN_IED_PRO_COMMU_ASK               4001  /** @ IED过程层通讯链路状态请求*/
#define  UN_IED_PRO_COMMU_REP               4002  /** @ IED过程层通讯链路状态回复*/
#define  UN_IED_PRO_GOOSE_CHG_REPORT        4003  /** @ IED过程层GOOSE变位报告*/
#define  UN_IED_PRO_ONLINE_ANA_ALARM_REPORT 4004  /** @ IED过程链路分析异常告层警*/
#define  UN_IED_PRO_OPTCAL_FIB_COMMU_REPORT 4005  /** @ IED过程层光纤连接状态报告*/


#define  UN_IED_PRO_SV_SNAPSHOT_ASK             4051  /** @ IED过程层SV快照数据采集请求*/
#define  UN_IED_PRO_SV_SNAPSHOT_REP             4052  /** @ IED过程层SV快照数据采集回复*/
#define  UN_IED_PRO_MULT_SV_SNAPSHOT_ASK        4053  /** @ IED过程层多个SV快照数据同步采集请求*/
#define  UN_IED_PRO_MULT_SV_SNAPSHOT_REP        4054  /** @ IED过程层多个SV快照数据同步采集回复*/
#define  UN_IED_PRO_SV_CONTINUOUS_SAMPLING_ASK  4055  /** @ IED过程层SV连续采样数据采集请求*/
#define  UN_IED_PRO_SV_CONTINUOUS_SAMPLING_REP  4056  /** @ IED过程层SV连续采样数据采集回复*/
#define  UN_IED_PRO_GOOSE_SNAPSHOT_ASK          4057  /** @ IED过程层GOOSE快照数据采集请求*/
#define  UN_IED_PRO_GOOSE_SNAPSHOT_REP          4058  /** @ IED过程层GOOSE快照数据采集回复*/
#define  UN_IED_PI_LINK_COMMU_REPORT            4059  /** @ IED虚端子连接状态分析报告 add by yzh 20170526*/
#define  UN_IED_NET_INTF_STATUS_REPORT          4060  /** @ IED端口连接状态报告 add by yzh 20170526*/
#define  UN_IED_NET_STATUS_REPORT               4061  /** @ IED网络连接状态报告 add by yzh 20170526*/
//////////////////////////////////////////////////////////////////////////枚举

///////////////////////////////////////////////////////////////////////////////////////////////////////////
////////2019-07-30 宋亮  智能录波器 BEGIN//////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////
/** @brief   ：录波、通用文件类型细分 */
#define  NX_IED_IEDFILETYPE_NORMAL			-1    /** @通用文件类型-普通通用文件*/
#define  NX_IED_IEDFILETYPE_CCD				501   /** @通用文件类型-CCD */
#define  NX_IED_IEDFILETYPE_ALARM			502   /** @通用文件类型-告警文件*/
#define  NX_IED_IEDFILETYPE_ARCHIVE			503   /** @通用文件类型-档案文件*/
#define  NX_IED_IEDFILETYPE_STATUS			504   /** @通用文件类型-状态文件*/
#define  NX_IED_IEDFILETYPE_CONTMSG			505   /** @通用文件类型-连续报文记录文件*/
#define  NX_IED_IEDFILETYPE_ABNMSG			506   /** @通用文件类型-异常报文记录文件*/
#define  NX_IED_IEDFILETYPE_TS_UNIFORM		507   /** @通用文件类型-暂态数据不一致报告文件*/
#define  NX_IED_IEDFILETYPE_DIR_FILE        508   /** @通用文件类型-指定目录召唤列表*/
#define  NX_IED_IEDFILETYPE_SGFILE			509   /** @通用文件类型-定值文件*/
#define  NX_IED_IEDFILETYPE_CFGFILE			510   /** @通用文件类型-配置文件*/
#define  NX_IED_IEDFILETYPE_CID			511   /** @通用文件类型-CID文件*/


#define  NX_IED_IEDFILETYPE_CSV_HMON		521   /** @通用文件类型-谐波监视文件(CSV) */

#define  NX_IED_IEDFILETYPE_WAVEEVENT		550		/** @录波文件类型-录波事件*/
#define  NX_IED_IEDFILETYPE_TS_WAVEFILE		551		/** @录波文件类型-分通道文件*/
#define  NX_IED_IEDFILETYPE_SS_WAVEFILE		552		/** @录波文件类型-稳态录波文件*/
#define  NX_IED_IEDFILETYPE_FT_WAVEBAK		553		/** @录波文件类型-故障录波全通道备份文件*/
#define  NX_IED_IEDFILETYPE_START_WAVEFILE	554		/** @录波文件类型-故障启动录波*/
#define  NX_IED_IEDFILETYPE_MAUL_WAVEFILE	555		/** @录波文件类型-故障手动录波*/
#define  NX_IED_IEDFILETYPE_FT_WAVEFILE		556		/** @录波文件类型-故障录波全通道文件*/
#define  NX_IED_IEDFILETYPE_BK_WAVEFILE		557		/** @录波文件类型-备份录波文件*/

#define  NX_IED_IEDFILETYPE_HM_EVENT_WAVEFILE		558		/** @录波文件类型-谐波事件录波*/
#define  NX_IED_IEDFILETYPE_HM_WAVEFILE		559					/** @录波文件类型-谐波周期录波*/

/** @系统类分组扩充*/
#define NX_SYS_EVENT_IEDS_SS_UNIFORM_RPT	5001	/**智能录波器-稳态数据不一致报告*/

/** @系统类分组扩充*/
#define  NX_SYS_EVENT_IED_TIME_CHG_RPT 		3114	  /** @IED时钟偏差更新报告*/
#define  NX_SYS_EVENT_SELF_HEART			3116	  /** @系统心跳*/

#define  NX_SYS_CTRL_SYSPARAM_CHG_ASK		3902      /** @系统配置更改命令*/
#define  NX_SYS_CTRL_SYSPARAM_CHG_REP		3903      /** @系统配置更改回复*/
#define  NX_SYS_CTRL_BATCH_CHECK_ASK		3904      /** @系统批量校核命令*/
#define  NX_SYS_CTRL_BATCH_CHECK_REP		3905      /** @系统批量校核回复*/

/** @事件类分组*/
#define  NX_IED_EVENT_ALARM_COMMU_SRV		2007	  /**  @IED通信服务告警  */
#define  NX_IED_EVENT_FILE_REPORT   		2101    /** @IED通用文件生成报告*/
#define  NX_IED_EVENT_OSCANALY_REPORT     2102  /** @IED录播文件分析结果通知*/

#define  UN_IED_NET_FLOW_STAT_REPORT		4062    /**		网络流量统计报告 */
#define  NX_GPS_STATUS_CHG_REPORT			4063	/**GPS对时状态变化报告*/

//added on 2021-10-9 by ljm begin
#define  NX_IED_CALL_LOG_ASK    33   /** @召唤IED日志请求*/
#define  NX_IED_CALL_LOG_REP    34   /** @召唤IED日志回复*/
//added on 2021-10-9 by ljm end
///////////////////////////////////////////////////////////////////////////////////////////////////////////
////////2019-07-30 宋亮  智能录波器 END//////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////


/** @brief  各应用程序的编号，全系统统一 */ 
enum TNx_App_Type
{
	NX_APP_MSG_SWITCH       = 1,        /**<  数据总线 */
	NX_APP_IEDS_ACQ         = 2,        /**<  设备信息采集*/
	NX_APP_DEV_ECU          = 3,        /**<  信息远传*/
	NX_APP_SAFARI           = 4,        /**<  监视操作中心*/
	NX_APP_DATA_MANAGER     = 5,        /**<  系统数据管理,同消息总线监视*/
	NX_APP_SYSRES_MONITOR   = 6,        /**<  系统资源监视*/
	NX_APP_CFG_TOOL         = 7,        /**<  系统调试配置工具*/
	NX_APP_GRIDFAULT_MANAGER= 8,        /**<  电网故障信息管理*/
	NX_APP_GRAPHIC_TOOL     = 9,        /**<  图形组态工具*/
	NX_APP_PI_ACQ           = 10,       /**<  过程层报文采集*/
	NX_APP_PI_NET_MONITOR   = 11,       /**<  网络通讯状态监视器*/
	NX_APP_IED_SCD_MONITOR  = 12,       /**<  IED_SCD监视器*/
	NX_APP_SAFETY_MEASURE_MNGR   = 13,   /**< 安措管理器*/
	NX_APP_APPLICATION_UNKOWN    = 1024,  /**< 一般应用*/
};
/** @brief  NX系统扩展的应用消息结构类型枚举 */
enum TNxAppStruct
{
	NX_T_COMMON_MSG = 0x4000,           /**< 通用类消息结构*/
	NX_T_EVENT_MSG   = 0x4001,          /**< 事件类消息结构*/
};
/** @brief 消息主题*/
enum TNxAppTopic
{
	NX_TOPIC_COMMAND        = 0x4000,        /**< 命令请求回复类主题*/
	NX_TOPIC_EVENT         = 0x4001,        /**< 事件通知类主题*/
	NX_TOPIC_SYS_MANAGER   = 0x4002,        /**<  系统管理主题*/
};

/** @brief 节点服务类型*/
enum TNxServiceMode
{
	MB_PROXY_SERVICE  = 0x01,		    /**<  收发代理服务*/
	MB_SWITCH_SERVICE = 0x02,			/**<  交换服务*/
};

/** @brief 节点管理的对象类型*/
enum TNxObjType
{
	NX_OBJ_TYPE_NX_ZIZHAN            = 0,		 /**< 远方子站*/
	NX_OBJ_TYPE_NX_PRIMEQU           = 1,		 /**< 一次设备*/
	NX_OBJ_TYPE_NX_IED               = 2,		 /**< IED*/
	NX_OBJ_TYPE_NX_ZHUZHAN           = 3,		 /**< 远方主站*/
	NX_OBJ_TYPE_UN_IED_HUILU         = 4,        /**< IED回路>*/
	NX_OBJ_TYPE_NX_LOCAL_HOST        = 100,		 /**< 本机*/
};
//////////////////////////////////////////////////////////////////////////通用消息结构定义
/** @brief  NX系统扩展的通用类应用消息结构—子对象队列定义*/
typedef struct _NX_COMMON_FIELD_STRUCT 
{
	int   n_field_id;                 /** @brief子对象id    举例: ied_obj_id*/
	char  c_field_name[128];          /** @子节点名称*/
	int   n_field_type;               /** @子对象数据类型*/
	char  c_value[128];               /** @字符值*/
	int   n_value;                    /** @整形值*/
	int   n_quality;                  /** @品质  1：运行  2：检修  3：无效数据，默认值 1*/
	unsigned int   n_valueucttm;      /** @发生时间  utc秒 默认值0*/
	unsigned int   n_valuems;         /** @发生时间  ms值 默认值 0*/
	double  f_backup;                 /** @浮点备用字段 默认值 0.0f*/
	_NX_COMMON_FIELD_STRUCT()
	{
		n_field_id = -1;
		_ZERO_MEM(c_field_name,sizeof(c_field_name));
		n_field_type = NX_APP_IEDS_ACQ;
		_ZERO_MEM(c_value,sizeof(c_value));
		n_value	  = -1;
		n_quality = NX_IED_DATA_QULITY_OPRATION;
		n_valuems = 0;
		f_backup = 0.0f;
	}
}NX_COMMON_FIELD_STRUCT;
/** @brief  NX系统扩展的通用类应用消息结构定义*/
typedef struct  _NX_COMMON_MESSAGE
{
	char  c_src_name[64];  /** @报文发送者,由nxmb填写*/
	char  c_dst_name[64];  /** @报文接收者, 有三类地址，广播地址:broadcast*/
	/** 未确定地址:为空，不填写；有明确的节点地址*/
	unsigned int   n_send_utctm;		/** @应用发送时间，UTC格式，由nxmb填写*/
	unsigned int   n_msg_topic;			/** @消息主题，默认为：NX_TOPIC _COMMON*/
	unsigned int   n_msg_type;			/** @应用消息类型，填写各种应用消息类型*/
	char  c_invoke_id[64];				/** @命令下发时带的命令编号*/
	unsigned int  n_msg_seq;			/** @同一命令下的子编号，从0开始，默认为0*/
	bool         b_lastmsg;				/** @是否是该命令号下的最后一个命令，默认true*/
	int   n_obj_id;						/** @一级级对象id         ied_obj_id*/
	int   n_obj_type;                   /** @一级对象类型*        详见TNxObjType枚举*/
	int   n_sub_obj_id;					/** @二级对象id           ld_code*/
	int   n_sub_sub_obj_id;				/** @三级级对象id         zone_code*/
	char  c_obj_pathname[128];          /** @对象路径           61850ref*/
	int    n_data_src;                  /** @数据来源  0：召唤上送   1:自动上送*/
	int    n_result;                    /** @操作结果    0:成功  1：操作失败  2：操作超时*/
	char  c_suffix[128];                /** @后缀*/
	int   n_backup;                      /** @备用字段*/

	vector<NX_COMMON_FIELD_STRUCT>  list_subfields; /** @子队列*/

	_NX_COMMON_MESSAGE()
	{
		_ZERO_MEM(c_src_name,sizeof(c_src_name));
		_ZERO_MEM(c_dst_name,sizeof(c_dst_name));
		n_send_utctm = 0;
		n_msg_topic  = NX_TOPIC_COMMAND;
		n_msg_type   = 0;
		_ZERO_MEM(c_invoke_id,sizeof(c_invoke_id));
		n_msg_seq = 0;
		b_lastmsg = true;
		n_obj_id = -1;
		n_obj_type = NX_OBJ_TYPE_NX_IED;
		n_sub_obj_id = -1;
		n_sub_sub_obj_id = -1;
		_ZERO_MEM(c_obj_pathname,sizeof(c_obj_pathname));
		n_data_src = 0;
		n_result = MB_COMMAND_OPRATION_RESULT_SUCCEED;
		n_backup = 0;
	}
}NX_COMMON_MESSAGE;
//////////////////////////////////////////////////////////////////////////事件消息结构定义
/** @brief系统扩展的事件类消息结构—子对象队列定义*/
typedef struct _NX_EVENT_FIELD_STRUCT 
{
	int   n_obj_id;                      /** @一级对象id*/
	int   n_obj_type;                    /** @一级对象类型 详见TNxObjType枚举*/
	int   n_sub_obj_id;                  /** @二级对象id*/
	int   n_sub_sub_obj_id;              /** @三级对象id*/
	char  c_field_name[128];             /** @子节点名称*/
	int   n_field_type;                  /** @子对象数据类型*/
	int   n_value;                       /** @事件值*/
	char  c_value_f[32];                 /** @字符值1 */
	char  c_value_s[32];                 /** @字符值2*/
	int   n_quality;                     /** @品质  1：运行  2：检修  3：无效数据,默认值运行值*/
	unsigned int   n_curvalueutctm;      /** @发生时间  utc秒*/
	unsigned int   n_curms;              /** @发生时间 毫秒*/
	unsigned int   n_fltstartutctm;      /** @装置启动时间  utc秒*/
	unsigned int   n_fltstartms;         /** @装置启动时间ms值*/
	int  n_gridfan;                      /** @电网故障序号*/
	double  f_backup;                    /** @备用字段*/
	_NX_EVENT_FIELD_STRUCT()
	{
		n_obj_id = -1;
		n_obj_type = NX_OBJ_TYPE_NX_IED;
		n_sub_obj_id = -1;
		n_sub_sub_obj_id = -1;
		_ZERO_MEM(c_field_name,sizeof(c_field_name));
		n_field_type = 0; //0:动作事件  1:故障参数
		n_value = -1;
		_ZERO_MEM(c_value_f,sizeof(c_value_f));
		_ZERO_MEM(c_value_s,sizeof(c_value_s));
		n_quality = NX_IED_DATA_QULITY_OPRATION;
		n_curvalueutctm = 0;
		n_curms = 0;
		n_fltstartutctm = 0;
		n_fltstartms = 0;
		n_gridfan = -1;
		f_backup = 0.0f;
	}
}NX_EVENT_FIELD_STRUCT;
/** @brief  NX系统扩展的应用消息结构定义*/
typedef struct  _NX_EVENT_MESSAGE
{
	char  c_src_name[64];         /** @brief报文发送者,由nxmb填写，使用者无需填写*/
	char  c_dst_name[64];         /** @brief报文接收者, 有三类地址，广播地址:broadcast*/
	//未确定地址:为空，不填写；有明确的节点地址
	unsigned int   n_send_utctm;       /** @brief应用发送时间，UTC格式，由nxmb填写*/
	unsigned int   n_msg_topic;       /** @brief消息主题，默认为: NX_TOPIC_EVENT*/
	unsigned int   n_msg_type;        /** @brief应用消息类型*/
	char           c_invoke_id[64];   /** @brief消息编号*/
	int            n_event_obj;       /** @brief 事件对象*/
	int            n_obj_type;        /** @brief 事件对象类型 详见TNxObjType枚举*/
	int            n_data_src;        /** @brief数据来源 0：召唤上送   1:自动上送*/
	char           c_suffix[128];     /** @brief后缀*/
	int            n_backup;		  /** @brief备用字段*/

	vector< NX_EVENT_FIELD_STRUCT >  list_subfields; /** @brief子队列*/

	_NX_EVENT_MESSAGE()
	{
		_ZERO_MEM(c_src_name,sizeof(c_src_name));
		_ZERO_MEM(c_dst_name,sizeof(c_dst_name));
		sprintf(c_dst_name,MB_DST_NODE_BROADCAST);
		n_send_utctm = 0;
		n_msg_topic  = NX_TOPIC_EVENT;
		n_msg_type = 0;
		_ZERO_MEM(c_invoke_id,sizeof(c_invoke_id));
		n_event_obj = -1;
		n_obj_type  = NX_OBJ_TYPE_NX_IED;
		n_data_src = MB_SEND_REASON_AUTO_SEND;
		_ZERO_MEM(c_suffix,sizeof(c_suffix));
		n_backup = 0;
	}
}NX_EVENT_MESSAGE;

typedef struct _NXMB_SUB_OBJ
{
	char subobjname[32];//[*]子对象名称
	int subobjtype;     //[*]子对象类型，详见TNxObjType枚举，默认为NX_OBJ_TYPE_NX_IED 智能设备
	int nreverse;       //[/]备用
	_NXMB_SUB_OBJ()
	{
		memset(subobjname,0,32);
		subobjtype = NX_OBJ_TYPE_NX_IED;
		nreverse = 0;
	}
}NXMB_SUB_OBJ;
typedef struct _NXMB_APPNODE
{
	int	              programtype;		 //[-]应用程序的type
	char	          programname[256];	 //[*]应用程序的名称，程序名
	int               programprocessid;  //[/-]应用程序的进程号
	vector<NXMB_SUB_OBJ>   subobjlist;   //[/-]节点管理的子对象
	vector<int>       nodeservicelist;   //[/]节点支持的服务
	char	          nodename[256];	 //[-]消息中心本地节点名称(唯一标识，由消息中心自动生成)，名称结构:机器名.节点名
	vector<string>    nodeiplist;		 //[/]机器IP地址
	char              appmodelversion[64];//[*]应用模型版本号
	char              mbmodelversion[64]; //[/]总线模型版本号 
	_NXMB_APPNODE()
	{
		programtype         = -1;
		memset(programname,0,256);
		programprocessid = 0;
		memset(nodename,0,256);
		memset(appmodelversion,0,64);
		sprintf(appmodelversion,"%s",APP_NXMB_MODEL_VERSION);
		memset(mbmodelversion,0,64);
	}
}NXMB_APPNODE;
//指定总线连接信息
typedef struct _NXMB_SWITCH_INFO
{
	vector<string>    busiplist;//[/]机器IP地址
	int               nbusport;
	_NXMB_SWITCH_INFO()
	{
		nbusport= 0;
	}
}NXMB_SWITCH_INFO;
//消息结构类型: MESSAGE_STRUCT_CONNECT
typedef struct _NXMB_NODES_STATE
{
	//MbProxy和MbSwitch之间连接关系发生的时间
	unsigned int 	  linkutctime;   //[-]uct时间，1970年以后的秒
	//消息中心MbProxy和MbSwitch之间连接关系的状态(true:连接正常/false:连接异常)
	bool 		  state; //[-]
	int           logstate;   //[-]节点当前登录状态 1:已登录  2:未登陆
	char		  local[256];  //[*]本地地址信息(IP:PORT, 比如:************:7064)
	char          remote[256]; //[*]远程地址信息(IP:PORT, 比如:************:7636)		
	_NXMB_NODES_STATE()
	{
		linkutctime = 0;
		state = false;  //断开
		logstate = MB_NODE_LOGSTATE_LOGIN;
		memset(local,0,256);
		memset(remote,0,256);
	}
}NXMB_NODE_STATE;


/** @brief  NX流数据消息_用于收发流数据,供数据转发使用*/
typedef struct  _UN_MB_STREAM_MESSAGE
{
	string str_sender;//发送者
	vector<string> str_reciever;//接收者
	int n_priority;//优先级
	int n_parse;   //结构体类型
	int n_topic;
	vector<BYTE> datastream;
	_UN_MB_STREAM_MESSAGE()
	{
		n_priority = 0x0b;
		n_topic = NX_TOPIC_EVENT;
		n_parse = NX_T_EVENT_MSG;
	}
}UN_MB_STREAM_MESSAGE;

//////////////////////////////////////////////////////////////////////////回调函数定义
/**
* @brief         提供给用户回调函数类型-通用消息通知回调
* @param[in]     无
* @param[out]    NX_EVENT_MESSAGE& 新事件  LPVOID 对象指针
* @return        int 
*/
typedef int (*PFUN_NX_MB_COMMON_MESSAGE) (NX_COMMON_MESSAGE&,LPVOID);

/**
* @brief         提供给用户回调函数类型-事件消息通知回调
* @param[in]     无
* @param[out]    NX_EVENT_MESSAGE& 新事件  LPVOID 对象指针
* @return        int 
*/

typedef int (*PFUN_NX_MB_EVENT_MESSAGE) (NX_EVENT_MESSAGE&,LPVOID);

/**
* @brief         提供给用户回调函数类型-节点状态信息通知回调
* @param[in]     无
* @param[out]    NXMB_NODE_STATE &  当前节点连接状态  LPVOID 对象指针
* @return        int 
*/
typedef int (*PFUN_MB_CONNET_MESSAGE) (NXMB_NODE_STATE &,LPVOID);

/**
* @brief         提供给用户回调函数类型-流数据回调
* @param[in]     无
* @param[out]    UN_MB_STREAM_MESSAGE &  流数据  LPVOID 对象指针
* @return        int 
*/
typedef int (*PFUN_UN_MB_STREAM_MESSAGE)(UN_MB_STREAM_MESSAGE & , LPVOID);

#endif