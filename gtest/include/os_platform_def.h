/**********************************************************************
* os_platform_def.h         author:jjl      date:15/08/2013            
*---------------------------------------------------------------------
*  note: 操作系统平台相关差异头文件                                                             
*  
**********************************************************************/

#ifndef _H_OS_PLATFORM_DEF_H_ 
#define _H_OS_PLATFORM_DEF_H_

/************************************************************************/
/*                          系统环境设定                                */
/************************************************************************/

///////////////////////////////////////////////////////////////HP UNIX环境

#ifdef __PLATFORM_HP_UNIX__
#      ifndef _POSIX_C_SOURCE
#      define _POSIX_C_SOURCE 1     // 开启POSIX标准
#      endif
#endif


///////////////////////////////////////////////////////////////AIX UNIX环境

#ifdef __PLATFORM_AIX_UNIX__
#      ifndef _THREAD_SAFE
#      define _THREAD_SAFE 1       // 开启线程安全标识
#      endif
#endif


/************************************************************************/
/*                             include标准头文件                        */
/************************************************************************/

////////////////////////////////////////////////////////////////C库文件
#include <stdio.h>  
#include <stdlib.h>  
#include <time.h>
#include <signal.h>
#include <errno.h>
#include <string.h>  
#include <ctype.h>
#include <stddef.h>
#include <stdarg.h>
#include <limits.h>

///////////////////////////////////////////////////////////////标准C++库文件
#include <iostream>
#include <strstream>
#include <string>
#include <iterator>
#include <cassert>
#include <fstream>
#include <list>
#include <deque>
#include <map>
#include <numeric>            
#include <set>
#include <stack>
#include <vector>
#include <algorithm>
#include <functional>
#include <memory>
#include <queue>
#include <complex>
#include <cctype>             
#include <new>
#include <cstdarg>             
#include <utility>
#include <cstdio>
#include <cstddef>
#include <cstdlib>
#include <cerrno>
#include <csignal>
#include <ctime>

////////////////////////////////////////////////////////////////命名空间
using namespace std;

////////////////////////////////////////////////////////////////数据类型
typedef char							int8;
typedef unsigned char					u_int8;
typedef short							int16;
typedef unsigned short					u_int16;
typedef int								int32;
typedef long long                       int64;
typedef unsigned int					u_int32;
typedef float							float32;
typedef double							float64;

/************************************************************************/
/*                       WINDOWS系统定义                                */
/************************************************************************/
#ifdef __PLATFORM_MS_WIN__

////////////////////////////////////////////////////////////////系统头文件
#  include <Winsock2.h>
#  include <process.h>
#  include <conio.h>
#  include <Windows.h>
#  include <io.h>
#  include <sys/stat.h>

////////////////////////////////////////////////////////////////常量定义

   const int INVALID_PTHREAD          = NULL;                   // 创建线程失败,返回类型


/////////////////////////////////////////////////////////////////宏定义     

   /** @brief         线程返回值,win下为DWORD*/
#  define THREAD_RET_VALUE            0                         

   /** @brief         定义send()发送标志*/
#  define SOCK_SEND_FUNC_FLAG         0         

   /** @brief         导出类或接口*/
#  define MY_DLL_EXPORT __declspec( dllexport )

   /** @brief         导入类或接口*/
#  define MY_DLL_IMPORT __declspec( dllimport )

/////////////////////////////////////////////////////////////////类型定义

   typedef  unsigned long             OS_PTHREAD_ID;            // 线程ID
   typedef  HANDLE                    OS_PTHREAD_HANDLE;        // 线程HANDLE 
   typedef  unsigned int              OS_PTHREAD_FUNC_RETTYPE;  // 线程入口函数类型
   typedef  HANDLE					  OS_PROCESS_HANDLE;        // 进程句柄
   typedef  CRITICAL_SECTION		  OS_MUTUAL_HANDLE;         // 临界区类型
   typedef  int                       SOCK_PARAM_LEN;           // socket中api参数长度类型
   typedef  HANDLE					  OS_SEM_RETVALUE;          // 信号量返回值
   typedef  HANDLE					  OS_SEM_HANDLE;            // 信号量句柄
   typedef  HANDLE					  OS_MODULE_HANDLE;         // 模块句柄
   typedef  HANDLE                    OS_SERIAL_PORT_HANDLE;    // 串口句柄
   typedef struct {
	       unsigned int nNum;
	       unsigned int nCondType;       
	       HANDLE		hEvent;
         }OS_COND_HANDLE;                                       // 条件变量结构

#endif 

/************************************************************************/
/*                        Linux系统特有定义                             */
/************************************************************************/

#ifdef __PLATFORM_OPEN_LINUX__ 

#   ifndef __PLATFORM_NONWIN_NORM__       
#   define __PLATFORM_NONWIN_NORM__                  // 包含公用定义
#   endif

// 对方连接关闭，本方继续发送时不抛出SIGPIPE信号
#  define SOCK_SEND_FUNC_FLAG        MSG_NOSIGNAL   

// linxu下线程mutex递归属性为PTHREAD_MUTEX_RECURSIVE_NP,unxi下为PTHREAD_MUTEX_RECURSIVE
#  define PTHREAD_MUTEX_RECURSIVE    PTHREAD_MUTEX_RECURSIVE_NP

// 网络通信api中参数类型(如地址长度)采用unsigned int
   typedef unsigned int            SOCK_PARAM_LEN;     

#endif

/************************************************************************/
/*                       SUN UNIX 系统特有定义                          */
/************************************************************************/

#ifdef __PLATFORM_SUN_UNIX__

#   ifndef __PLATFORM_NONWIN_NORM__       
#   define __PLATFORM_NONWIN_NORM__                  // 包含公用定义
#   endif

#   include <sys/sockio.h>
#   include <sys/param.h>
#   include <net/if_arp.h> 

#   define SOCK_SEND_FUNC_FLAG        0               //  定义send()发送标志
#   define SOL_IP                     IPPROTO_IP      //  socket level

// 网络通信api中参数类型(如地址长度)采用unsigned int
   typedef unsigned int               SOCK_PARAM_LEN;

//  采用poll()设置超时
#   ifndef _POLLING_TMOUT_
#   define _POLLING_TMOUT_	
#   endif	

#endif

/************************************************************************/
/*                        AIX UNIX 系统特有定义                         */
/************************************************************************/

#ifdef __PLATFORM_AIX_UNIX__

#   ifndef __PLATFORM_NONWIN_NORM__       
#   define __PLATFORM_NONWIN_NORM__                  // 包含公用定义
#   endif

#   include	<termio.h>
#   include <net/if.h>

#   define SOCK_SEND_FUNC_FLAG        0               //  定义send()发送标志
#   define SOL_IP                     IPPROTO_IP      //  socket level

//  网络通信api中参数类型(如地址长度)采用ulong32	 
    typedef __ulong32_t               SOCK_PARAM_LEN; //  aix define in inttypes.h 

//  采用select()设置超时
#   ifndef _SELECTING_TMOUT_
#   define _SELECTING_TMOUT_	
#   endif	

//  AIX为大端模式(数据的高位，保存在内存的低地址中，而数据的低位，保存在内存的高地址中)
#   ifndef _PC_BIG_ENDIAN_
#   define _PC_BIG_ENDIAN_
#   endif	 

#endif

/************************************************************************/
/*                        HP UNIX 系统特有定义                          */
/************************************************************************/

#ifdef __PLATFORM_HP_UNIX__

#   ifndef __PLATFORM_NONWIN_NORM__       
#   define __PLATFORM_NONWIN_NORM__                  // 包含公用定义
#   endif

#   include <net/if.h>

#   define SOCK_SEND_FUNC_FLAG        0               //  定义send()发送标志
#   define SOL_IP                     IPPROTO_IP      //  socket level

// 网络通信api中参数类型(如地址长度)采用 int
	typedef  int                      SOCK_PARAM_LEN;
		
//  采用select()设置超时
#   ifndef _SELECTING_TMOUT_
#   define _SELECTING_TMOUT_	
#   endif	

//  Itanium处理器为大端模式
#   ifndef _PC_BIG_ENDIAN_
#   define _PC_BIG_ENDIAN_
#   endif	 

#endif

/************************************************************************/
/*               非windows平台相关共享定义(linxu/unix系统)              */
/************************************************************************/

#ifdef __PLATFORM_NONWIN_NORM__         // 非windows系统(linxu/unix)的共有定义

	/////////////////////////////////////////////////////////////系统包含头文件
#  include <sys/types.h>  
#  include <netinet/in.h>  
#  include <sys/socket.h>  
#  include <sys/wait.h>  
#  include <arpa/inet.h>       
#  include <unistd.h>         
#  include <sys/select.h>
#  include <sys/time.h>
#  include <sys/ioctl.h> 
#  include <termios.h>
#  include <netdb.h>
#  include <pthread.h>
#  include <fcntl.h>
#  include <sys/ipc.h>
#  include <sys/msg.h>
#  include <sys/stat.h>
#  include <dirent.h>
#  include <semaphore.h>
#  include <sys/ipc.h>
#  include <sys/sem.h>
#  include <signal.h>
#  include <dlfcn.h>  
#  include <termios.h>    
#  include <poll.h>

	//////////////////////////////////////////////////////////////////常量定义

	const int INVALID_SOCKET        = -1;             // 无效socket句柄(与win一致)
	const int SOCKET_ERROR          = -1;             // socket错误(与win一致)
	 
	const int INVALID_PTHREAD       = -1;             // 创建线程失败
 
	#  ifndef TRUE
		const int TRUE                  = 1;              // 与win 一致
	#  endif

	#  ifndef FALSE
		const int FALSE                 = 0;              // 与win 一致 
	#  endif



	///////////////////////////////////////////////////////////////////宏定义

# define NULL                    0

	/** @brief         线程的返回值为 null*/
# define THREAD_RET_VALUE        NULL             

	/** @brief         linux/unix下WINAPI(__stdcall) 无效*/
# define WINAPI                                    

	/** @brief         导出类或接口*/
#  define MY_DLL_EXPORT 

	/** @brief         导入类或接口*/
#  define MY_DLL_IMPORT 

	/** @brief         输入类型参数*/
#  define   IN      

	/** @brief         输出类型参数*/
#  define   OUT

	/** @brief         可选参数*/
#  define   OPTIONAL

	//////////////////////////////////////////////////////////////////类型定义

	/** @note                       常用数据类型(与win一致)*/
	typedef int                     SOCKET;          
	typedef int                     BOOL;            
	typedef void                    *LPVOID;         
	typedef char*					LPTSTR;           
	typedef unsigned char           BYTE;
	typedef unsigned short          WORD;
	typedef unsigned int            UINT;
	typedef unsigned long			DWORD;
	typedef unsigned char			u_char;
	typedef unsigned short          u_short;
	typedef unsigned int            u_int;
	typedef unsigned long           u_long;

	/** @note                       自定义类型*/
	typedef pthread_t               OS_PTHREAD_ID;    // 线程ID与线程句柄一致,都为pthread_t类型
	typedef pthread_t               OS_PTHREAD_HANDLE;// 线程创建后返回值为pthread_t类型
	typedef void *                  OS_PTHREAD_FUNC_RETTYPE;// 线程入口函数类型
	typedef pid_t					OS_PROCESS_HANDLE; // 进程句柄
	typedef pthread_mutex_t         OS_MUTUAL_HANDLE;  // 互斥体句柄
	typedef pthread_cond_t		    OS_COND_HANDLE;    // 条件量句柄
	typedef int					    OS_SEM_RETVALUE;   // 信号量返回值
	typedef sem_t					OS_SEM_HANDLE;     // 信号量结构句柄
	typedef void*					OS_MODULE_HANDLE;  // 模块句柄
	typedef int                     OS_SERIAL_PORT_HANDLE;//串口句柄

#endif


/************************************************************************/
/*                         宏函数定义                                   */
/************************************************************************/

/** @brief              取最小值*/
#define  _MIN_VAL(a,b) ( (a)<(b) ? (a) : (b) )   

/** @brief              取最大值*/
#define  _MAX_VAL(a,b) ( (a)>(b) ? (a) : (b) )

/** @brief              清空指定内存*/
#define  _ZERO_MEM(p,l)  memset(p , 0, l)
    
#ifdef _PC_BIG_ENDIAN_

/** @brief               16位数高低位反转*/
#      define _REVERSE_BYTE_ORDER_16(x) \
	         ( ( ((u_short)(x) & 0xff00) >> 8 ) |  \
	           ( ((u_short)(x) & 0x00ff) << 8 )    \
	         )

/** @brief               32位数高低位反转*/
#      define _REVERSE_BYTE_ORDER_32(x) \
	     ( ( ( (unsigned)(x) & 0xff000000 ) >> 24 ) |   \
	       ( ( (unsigned)(x) & 0x00ff0000 ) >>  8 ) |   \
		   ( ( (unsigned)(x) & 0x0000ff00 ) <<  8 ) |   \                               
	       ( ( (unsigned)(x) & 0x000000ff ) << 24 )     \
	     )

/** @brief                64位高低位反转*/
#      define _REVERSE_BYTE_ORDER_64(x) \
	    (  ( ( (unsigned)(x) & 0xff00000000000000 ) >> 56 ) |   \
		   ( ( (unsigned)(x) & 0x00ff000000000000 ) >> 40 ) |   \
		   ( ( (unsigned)(x) & 0x0000ff0000000000 ) >> 24 ) |   \
		   ( ( (unsigned)(x) & 0x000000ff00000000 ) >>  8 ) |   \
		   ( ( (unsigned)(x) & 0x00000000ff000000 ) <<  8 ) |   \
		   ( ( (unsigned)(x) & 0x0000000000ff0000 ) << 24 ) |   \
		   ( ( (unsigned)(x) & 0x000000000000ff00 ) << 40 ) |   \
		   ( ( (unsigned)(x) & 0x00000000000000ff ) << 56 )     \
		)        
                 
#else
#      define _REVERSE_BYTE_ORDER_16(x)
#      define _REVERSE_BYTE_ORDER_32(x)
#      define _REVERSE_BYTE_ORDER_64(x)
#endif

/************************************************************************/
/*                        通用常量、结构定义                            */
/************************************************************************/

//////////////////////////////////////////////////////////////////文件相关
/** @brief                     文件每行最大长度*/
const int MAX_FILE_EVERY_LINE_LEN = 1024;                    

/** @brief                     文件错误信息最大长度*/
const int MAX_FILE_ERRINF_LEN    = 255;                     

/** @brief                     文件名最大长度*/
const int MAX_FILE_NAME_LEN      = 255;                     

/** @brief                     文件状态信息*/
typedef struct _FILE_PROPERTY_INF
{
	_FILE_PROPERTY_INF():nLastTime(0),nSize(0),nReserve1(0)
	{
		_ZERO_MEM(chName,255);
		_ZERO_MEM(cReserve1,48);
	}
	/** @brief                 文件名*/
	char chName[MAX_FILE_NAME_LEN];

	/** @brief                 文件最后更新时间*/
	time_t nLastTime;

	/** @brief                 文件大小*/
	int   nSize;

	/** @brief                 整形保留*/
	int   nReserve1;

	/** @brief                 字符型保留*/
	char  cReserve1[MAX_FILE_NAME_LEN];
}FILE_PROPERTY_INF;
//////////////////////////////////////////////////////////////////其它

/** @brief          时间信息*/
typedef struct _MY_TIME_INFO
{
	_MY_TIME_INFO():nYear(0),nMon(0),nDayOfWeek(0),nDay(0),
		nHour(0),nMin(0),nSec(0),nMilSec(0)
	{}
	WORD    nYear;             // 年
    WORD    nMon;              // 月
    WORD    nDayOfWeek;        // 星期
    WORD    nDay;              // 日
    WORD    nHour;             // 时
    WORD    nMin;              // 分
    WORD    nSec;              // 秒
    WORD    nMilSec;           // 毫秒
} MY_TIME_INFO;


#endif

