#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <string>
#include <vector>
#include <cstring>
#include <cstdint>
#include <algorithm>
#include <cctype>
#include "nx_model_def.h"
#include "ec_pro_common_def.h"
#include "NXEcProAsdu.h"
// 基础类型定义
typedef uint8_t u_int8;
typedef uint16_t u_int16;
typedef uint32_t u_int32;
typedef int32_t int32;

// 参数修饰符
#define IN
#define OUT

/** @brief         ASDU106信息*/
typedef struct _ASDU106_INFO
{
	/** @brief         地址信息*/
	ASDU_ADDR         Addr;
	/** @brief         信息体*/
	ASDU_INFO_OBJ     InfoObj;
	/** @brief         文件类型（1-定值文件）*/
	u_int8            nFileType;
	/** @brief         时间*/
	ASDU_TIME         InfoTime;
	/** @brief         文件名*/
	string            strWavFileName;
	/** @brief         备用字符*/
	string            strReserve;

	_ASDU106_INFO()
	{
		nFileType = 1; // 默认为定值文件
	}
}ASDU106_INFO;

// 日志记录类Mock
class MockLogRecord
{
public:
    MOCK_METHOD(void, RecordTraceLog, (const char* msg, const char* className), ());
    MOCK_METHOD(void, RecordErrorLog, (const char* msg, const char* className), ());
};

// 模型查询接口Mock
class MockModelSeek
{
public:
    MOCK_METHOD(bool, GetBasicCfg, (BASIC_CFG_TB& cfg), ());
    MOCK_METHOD(const SUBSTATION_TB*, GetSubStationBasicCfg, (), ());
    MOCK_METHOD(const IED_TB*, GetIedBasicCfg, (u_int32 iedId), ());
};

// 时间转换类Mock
class MockTimeConvert
{
public:
    MockTimeConvert(u_int32 utc, u_int16 ms) : m_utc(utc), m_ms(ms) {}
    
    MOCK_METHOD(void, GetCP56TIMe, (std::string& timeStr), ());
    
private:
    u_int32 m_utc;
    u_int16 m_ms;
};

// 测试工具函数
class TestUtils
{
public:
    // 创建测试用的PRO_FRAME_BODY
    static PRO_FRAME_BODY CreateTestFrameBody(u_int16 addr = 1, const std::string& fileName = "test.txt");
    
    // 创建测试用的NX_EVENT_MESSAGE
    static NX_EVENT_MESSAGE CreateTestEventMessage(u_int32 msgType = NX_IED_EVENT_FILE_REPORT, 
                                                   u_int32 iedId = 1, 
                                                   const std::string& fileName = "test.txt");
    
    // 创建测试用的ASDU106_INFO
    static ASDU106_INFO CreateTestAsdu106Info(const std::string& fileName = "test.txt");
    
    // 验证PRO_FRAME_BODY的基本字段
    static void VerifyFrameBodyBasics(const PRO_FRAME_BODY& body, u_int8 expectedType);
    
    // 创建测试文件目录结构
    static void CreateTestFileStructure(const std::string& basePath);
    
    // 清理测试文件
    static void CleanupTestFiles(const std::string& basePath);
};

// 字符串工具函数
inline char* _strstr_nocase(char* str1, const char* str2)
{
    if (!str1 || !str2) return nullptr;
    
    std::string s1(str1);
    std::string s2(str2);
    
    // 转换为小写进行比较
    std::transform(s1.begin(), s1.end(), s1.begin(), ::tolower);
    std::transform(s2.begin(), s2.end(), s2.begin(), ::tolower);
    
    size_t pos = s1.find(s2);
    if (pos != std::string::npos) {
        return str1 + pos;
    }
    return nullptr;
}

// 文件系统工具函数
inline int sy_format_file_path(const char* path, char* formatted_path)
{
    if (!path || !formatted_path) return -1;
    strcpy(formatted_path, path);
    return 0;
}

inline int sy_get_file_property(const char* filename, FILE_PROPERTY_INF* fileInfo)
{
    if (!filename || !fileInfo) return -1;
    
    // 模拟文件存在
    fileInfo->strFileName = filename;
    fileInfo->nFileSize = 1024;
    fileInfo->nCreateTime = 1640995200; // 2022-01-01 00:00:00
    fileInfo->nModifyTime = 1640995200;
    
    return 0;
}

inline int sy_get_file_name(const char* fullPath, char* path, char* name, char* ext)
{
    if (!fullPath) return -1;
    
    std::string full(fullPath);
    size_t lastSlash = full.find_last_of('/');
    size_t lastDot = full.find_last_of('.');
    
    if (path) {
        if (lastSlash != std::string::npos) {
            strcpy(path, full.substr(0, lastSlash).c_str());
        } else {
            strcpy(path, "");
        }
    }
    
    if (name && ext) {
        std::string filename = (lastSlash != std::string::npos) ? 
                              full.substr(lastSlash + 1) : full;
        
        if (lastDot != std::string::npos && lastDot > lastSlash) {
            strcpy(name, filename.substr(0, lastDot - (lastSlash + 1)).c_str());
            strcpy(ext, filename.substr(lastDot + 1).c_str());
        } else {
            strcpy(name, filename.c_str());
            strcpy(ext, "");
        }
    }
    
    return 0;
}
