/**********************************************************************
* ec_common_def.h         author:jjl      date:28/08/2013            
*---------------------------------------------------------------------
*  note: 对外通信进程公用定义头文件                                                              
*  
**********************************************************************/

#ifndef _H_EC_COMMON_DEF_H_ 
#define _H_EC_COMMON_DEF_H_

//////////////////////////////////////////////////////////////////平台头文件包含
#include "os_platform_def.h"
#include "os_platform_fun.h"
#include "FileOperate.h"
#include "LogRecord.h"
#include "ThreadMutualLock.h"
#include "MyDeque.h"
#include "MyList.h"


///////////////////////////////////////////////////////////////////项目头文件包含
#include "inxmb_def.h"
#include "nx_errno_def.h"
#include "NXECObject.h"

//////////////////////////////////////////////////////////////////常量定义

/** @brief         事件队列最大长度*/
const int MAX_EVENT_DEQUE_LEN = 20000;

/** @brief         通信状态正常(通信:1:正常 0:断开 2：未知）*/ 
const int COMMU_STATUS_ONLINE  = 1;

/** @brief         通信状态断开*/
const int COMMU_STATUS_OUTLINE = 0;

/** @brief         通信状态未知*/
const int COMMU_STATUS_UNKNOWN = 2;

/////////////////////////////////////////////////////////////////函数指针定义

/** @brief         系统通用消息处理*/
typedef int       (*PFUNC_ON_NXCOMMON_MSG_HANDLE)(IN LPVOID ,IN NX_COMMON_MESSAGE &);

/** @brief         系统事件消息处理*/
typedef int       (*PFUNC_ON_NXEVENT_MSG_HANDLE)(IN LPVOID , IN NX_EVENT_MESSAGE &);

//////////////////////////////////////////////////////////////////宏、常量定义

//////////////////////////////////////////////公用共享库名称
#   ifdef __PLATFORM_MS_WIN__   // win平台

/** @brief                                    与总线交换数据共享库名称*/
#        define  SHARE_LIB_BUS_SWAP              "nx_ec_bus_swap.dll"
/** @brief                                    服务中介共享库名称*/
#        define  SHARE_LIB_SRV_MEDITOR           "nx_ec_srv_mediator.dll"
/** @brief                                    对外通信结点管理共享库名称*/
#        define  SHARE_LIB_NODE_MGR              "nx_ec_node_mgr.dll"
/** @brief                                    网络监听共享库名称*/
#        define  SHARE_LIB_NET_LISTEN            "nx_ec_net_listen.dll"
/** @brief                                    模型访问共享库名称*/
#        define  SHARE_LIB_MODEL_ACCESS          "nx_ec_model_access.dll"

#   else                        // 非win平台

/** @brief                                    与总线交换数据共享库名称*/
#        define  SHARE_LIB_BUS_SWAP              "libnx_ec_bus_swap.so"
/** @brief                                    服务中介共享库名称*/
#        define  SHARE_LIB_SRV_MEDITOR           "libnx_ec_srv_mediator.so"
/** @brief                                    对外通信结点管理共享库名称*/
#        define  SHARE_LIB_NODE_MGR              "libnx_ec_node_mgr.so"
/** @brief                                    网络监听共享库名称*/
#        define  SHARE_LIB_NET_LISTEN            "libnx_ec_net_listen.so"
/** @brief                                    模型访问共享库名称*/
#        define  SHARE_LIB_MODEL_ACCESS          "libnx_ec_model_access.so"

#   endif


//////////////////////////////////////////////////////////////////结构定义

/** @brief         通信方式枚举*/
typedef enum _EC_COMMU_MODE
{
	EC_MODE_SERIALPORT    = 1,      /**<  串口通信 */       
	EC_MODE_TCP           = 2,      /**<  TCP通信 */       
	EC_MODE_UDP_BROADCAST = 3,      /**<  UDP广播通信 */    
	EC_MODE_UDP_MULTICAST = 4,      /**<  UDP组播通信 */ 
	EC_MODE_UDP           = 5,      /**<  UDP点对点通信 */    
}EC_COMMU_MODE;

/** @brief         状态类型枚举*/
typedef enum  _EC_STATUS_TYPE
{
	EC_STATUS_UNKOWN = 0,   /**<  未知 */
	EC_COMMU_STATUS  = 1,   /**<  通信状态 */
	EC_RUN_STATUS    = 2,   /**<  运行状态 */
}EC_STATUS_TYPE;

/** @brief              设备种类枚举*/
typedef enum _DEV_CATEGORY
{
	//DEV_UNKNOW     = 0,                        /**<      未知类型 */
	DEV_PRIMARY    = NX_OBJ_TYPE_NX_PRIMEQU,     /**<      一次设备 */
	DEV_SECOND     = NX_OBJ_TYPE_NX_IED,         /**<      二次设备 */
	DEV_STATION    = NX_OBJ_TYPE_NX_ZIZHAN,      /**<      子站 */
	DEV_R_CLIENT   = NX_OBJ_TYPE_NX_ZHUZHAN,     /**<      远方客户端,如主站、自动化后台 */
	DEV_L_CIIENT   = NX_OBJ_TYPE_NX_LOCAL_HOST   /**<      本地客户端 */

}DEV_CATEGORY;

/** @brief         状态信息结构*/
typedef struct _EC_STATUS_INFO
{
	/** @brief           设备种类: */
	DEV_CATEGORY         eDevType;

	/** @brief           设备ID*/
	int                  nDevID;

	/** @brief           通道编号*/
	int                  nChannelID;   

	/** @brief           状态类型*/
	EC_STATUS_TYPE       eStatusType;

	/** @brief           状态值*/
	//(通信:1:正常 0:断开 2：未知）
	//(运行:0: 检修 1: 停运 2: 投运 3: 未接入 4: 调试(对码表) 其它:未知)
	int                  nStatus;

	/** @brief           状态变化原因*/
	int                  nReason;

	/** @brief           状态变化时间*/
	string               strTime;

	/** @brief           备用(启用为时间毫秒值)*/
	int                  nReaseve;

	/** @brief           字符备用*/
	char                 chReserve[64];

	_EC_STATUS_INFO()
	{
		eDevType    = DEV_SECOND;
		eStatusType = EC_COMMU_STATUS;
		nDevID      = -1;
		nChannelID  = -1;
		nStatus     = -1;
		nReason     = -1;
		strTime     = "";
		nReason     = 0;
		_ZERO_MEM(chReserve,64);
	}

}EC_STATUS_INFO;

/** @brief         通道状态变化回调*/
typedef int       (*PFUNC_ON_CHANNEL_STATUS_CHG)(IN LPVOID,IN EC_STATUS_INFO&);


/** @brief               设备通用唯一标识结构*/
typedef struct _DEV_UUID
{
	/** @brief           设备种类: */
	DEV_CATEGORY         eDevType;

	/** @brief           设备ID*/
	int                  nDevID;

	/** @brief           备用*/
	int                  nReserve;

	/** @brief           备用*/
	char                 chReserve[48];
	_DEV_UUID()
	{
		eDevType  =  DEV_SECOND;
		nDevID    =  -1;
		nReserve  =   0;
		_ZERO_MEM(chReserve,48);
	}

	// ==操作符重载
	bool operator==(const _DEV_UUID & Dev)
	{
		if( ( eDevType == Dev.eDevType ) && (nDevID == Dev.nDevID) )
			return true;
		else
			return false;
	}

}DEV_UUID;


////////////////////////////////////////////////////////////////类型定义

/** @brief         信息订阅列表类型*/
typedef vector<int>   EC_INFO_ORDER_LIST;

/** @brief         设备订阅列表类型*/
typedef vector<DEV_UUID>   EC_DEV_ORDER_LIST;

/** @brief         通用消息列表*/
typedef list <NX_COMMON_MESSAGE> NX_COMMON_MSG_LIST;

/** @brief         通用消息子集列表*/
typedef vector<NX_COMMON_FIELD_STRUCT> NX_COMMON_MSG_SUBFILED_LIST;

/** @brief         事件消息列表*/
typedef list <NX_EVENT_MESSAGE> NX_EVENT_MSG_LIST;

/** @brief         事件消息自给列表*/
typedef vector< NX_EVENT_FIELD_STRUCT > NX_EVENT_MSG_SUBFILED_LIST;

#endif