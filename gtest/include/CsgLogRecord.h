/**********************************************************************
* CCsgLogRecord.h        author:flood      date:04/03/2015            
*---------------------------------------------------------------------
*  note:南网日志类声明头文件                                                       
*  
**********************************************************************/
#ifndef _H_LOG_CSG_RECORD_H_ 
#define _H_LOG_CSG_RECORD_H_

//////////////////////////////////////////////////////////////////////////包含头文件
#include "os_platform_def.h" /**<  包含平台定义相关头文件*/

#include "ThreadMutualLock.h"
#include "FileOperate.h"     /**< 文件操作*/
using namespace std;

//模块编码-三个字节，只可使用英文字母和阿拉伯数字
//日志名格式：最后三个字符为自定义标识符，对第一个字符进行定义，各模块使用时，在初始化接口时要求使用
//RUNLOG_2014_05_07_14_27_34_874_A00.txt
//A**:采集器模块
//B**:对上通讯模块
//C**:配置工具
//D**:客户端界面
//E**:就地系统监视模块
//F**:历史数据管理模块
//////////////////////////////////////////////////////////////////////////宏定义
/** @brief             日志文件最大容量(单位:字节,5M)*/
#define  MAX_CSG_LOG_RCORD_FILE_SIZE     1000000*5
/** @brief             日志文件最小尺寸(单位:字节,1M)*/
#define  MIN_CSG_LOG_RCORD_FILE_SIZE     1000000

#define  CSG_LOG_ROLE_RUN	 0/**<  运行日志 */
#define  CSG_LOG_ROLE_OPR    1/**<  操作日志 */
#define  CSG_LOG_ROLE_MNT    2/**<  维护日志 */
/**<  日志文件中的第一行 */
#define  CSG_LOG_FILE_FIRST_LINE  "#ID,TimeStamp,Attribute,Sponsor,Behavior,Target,DataType,DataObject,DataAuciliary,SubBehavior,BehaviorResult,AttachInfo\r\n"
//////////////////////////////////////////////////////////////////////////配置结构
/** @brief          日志初始化配置结构*/
typedef struct _CSG_LOG_RECORD_CFG
{
	/** @brief 根路径*/
	string strRootPath;
	/** @brief 模块名*/
	string  strModulName;
	/** @brief  运行日志保存天数*/
	unsigned int  nOprLogSaveDays;
	/** @brief 文件指定可以达到的最大值(单位:kB字节) */	
	long      nMaxLogFileSize;
	/** @brief 日志是否写入文件 0:为不写入  大于0表示写入*/	
	int      nWrite2File;
	/** @brief  日志是否打印到屏幕*/
	bool      bPrint2Screen;
	_CSG_LOG_RECORD_CFG()
	{
		strRootPath     =  "../CsgLog/";//默认根路径，本地模块无需更改
		strModulName    = "CSG";               //模块编码,遵循模块编码规范，各模块必须按规则填写
		nOprLogSaveDays = 100;                 //运行日志保留时间，至少90天
		nMaxLogFileSize =  MAX_CSG_LOG_RCORD_FILE_SIZE;
		nWrite2File     = 0;
		bPrint2Screen   = true;
	}
}CSG_LOG_RECORD_CFG;
/** @brief日志单元属性 */
enum CSG_LOG_UNIT_ATTRIBUTE
{
	CSG_LOG_UNIT_ATTRIBUTE_ERROR = 0,   /**<  错误 */
	CSG_LOG_UNIT_ATTRIBUTE_ALARM = 1,   /**<  告警 */
	CSG_LOG_UNIT_ATTRIBUTE_READ  = 2,   /**<  读取 */
	CSG_LOG_UNIT_ATTRIBUTE_WRITE = 3,   /**<  写入 */
	CSG_LOG_UNIT_ATTRIBUTE_OPR   = 4,   /**<  运行 */
	CSG_LOG_UNIT_ATTRIBUTE_MNT   = 5,   /**<  维护 */
	CSG_LOG_UNIT_ATTRIBUTE_OTHER = 999  /**<  其它 */
};
/** @brief日志单元动作 */
enum CSG_LOG_UNIT_BEHAVIOR
{
	/*报文接收过程：解析；
	文件传输过程：读取、召唤、接收、上送；
	遥控过程：遥控、复归、投入、退出；
	定值操作过程：召唤、接收、下传、固化；
	定值区操作过程：召唤、接收、切换、固化；
	文件维护过程：删除、新建；
	总召唤过程：总召唤；
	子站维护过程：新建、删除、升级、扩建、修改。*/
	CSG_LOG_UNIT_BEHAVIOR_DATA_RCV_ANALYZE = 0,/**<  报文接收过程:解析 */
	CSG_LOG_UNIT_BEHAVIOR_FILE_TRANS_READ  = 1, /**<   文件传输过程：读取 */
	CSG_LOG_UNIT_BEHAVIOR_FILE_TRANS_CALL  = 2, /**<   文件传输过程：召唤 */
	CSG_LOG_UNIT_BEHAVIOR_FILE_TRANS_RCV   = 3, /**<   文件传输过程：接收 */
	CSG_LOG_UNIT_BEHAVIOR_FILE_TRANS_UPLOAD= 4, /**<   文件传输过程：上送 */
	CSG_LOG_UNIT_BEHAVIOR_YK_YK            = 5, /**<   遥控过程：遥控 */
	CSG_LOG_UNIT_BEHAVIOR_YK_RESET         = 6, /**<   遥控过程：复归 */
	CSG_LOG_UNIT_BEHAVIOR_YK_TURN_ON       = 7, /**<   遥控过程：投入 */
	CSG_LOG_UNIT_BEHAVIOR_YK_TURN_OFF      = 8, /**<   遥控过程：退出 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_CALL     = 9, /**<   定值操作过程：召唤 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_RCV      = 10, /**<   定值操作过程：接收 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_DOWNLOAD = 11, /**<   定值操作过程：下传 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_SOLIDIFICATION = 12, /**<   定值操作过程：固化 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_ZONE_CALL      = 13, /**<   定值区操作过程：召唤 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_ZONE_RCV       = 14, /**<   定值区操作过程：接收 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_ZONE_SWITCH    = 15, /**<   定值区操作过程：切换 */
	CSG_LOG_UNIT_BEHAVIOR_SETTING_ZONE_SOLIDIFICATION = 16, /**<   定值区操作过程：固化 */
	CSG_LOG_UNIT_BEHAVIOR_FILE_DELETE = 17, /**<   文件维护过程：删除 */
	CSG_LOG_UNIT_BEHAVIOR_FILE_CREATE = 18, /**<   文件维护过程：新建 */
	CSG_LOG_UNIT_BEHAVIOR_GEN_CALL    = 19, /**<   总召唤过程：总召唤 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_CREATE  = 20, /**<   子站维护过程：新建 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_DELETE  = 21, /**<   子站维护过程：删除 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_UPDATE  = 22, /**<   子站维护过程：升级 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_EXTEND  = 23, /**<   子站维护过程：扩建 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_MODIFY  = 24, /**<   子站维护过程：修改 */
	//added on 2017-9-6 by ljm begin
	CSG_LOG_UNIT_BEHAVIOR_MNT_LOGIN  = 25, /**<   子站维护过程：登录 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_LOGOUT  = 26, /**<   子站维护过程：退出 */

	CSG_LOG_UNIT_BEHAVIOR_MNT_ADD_USRS = 27,/**<   子站维护过程：新增用户组 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_DEL_USRS = 28,/**<   子站维护过程：删除用户组 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_UPDATE_USRS = 29,/**<   子站维护过程：更新用户组 */

	CSG_LOG_UNIT_BEHAVIOR_MNT_ADD_USR = 30,/**<   子站维护过程：新增用户 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_DEL_USR = 31,/**<   子站维护过程：删除用户 */
	CSG_LOG_UNIT_BEHAVIOR_MNT_UPDATE_USR = 32,/**<   子站维护过程：更新用户 */
	//added on 2017-9-6 by ljm end

	CSG_LOG_UNIT_BEHAVIOR_OTHER       = 999, /**<   其它 */
};
/** @brief日志单元数据类型 */
enum CSG_LOG_UNIT_DATATYPE
{
	CSG_LOG_UNIT_DATATYPE_SETTING          = 0, /**<  定值(组) */
	CSG_LOG_UNIT_DATATYPE_SETTING_GROUP    = 1, /**< 定值区(组) */
	CSG_LOG_UNIT_DATATYPE_ANALOG           = 2, /**< 模拟量(组) */
	CSG_LOG_UNIT_DATATYPE_FAULT_PARAM      = 3, /**< 故障参数(组) */
	CSG_LOG_UNIT_DATATYPE_SOFTBORAD        = 4, /**< 软压板(组) */
	CSG_LOG_UNIT_DATATYPE_YAOXIN           = 5, /**< 遥信(组)*/
	CSG_LOG_UNIT_DATATYPE_DIGIT            = 6, /**<开关量(组)*/
	CSG_LOG_UNIT_DATATYPE_EVENT            = 7, /**<事件(组)*/
	CSG_LOG_UNIT_DATATYPE_ALARM            = 8, /**<告警(组)*/
	CSG_LOG_UNIT_DATATYPE_HIS_DATA         = 9, /**<历史数据*/
	CSG_LOG_UNIT_DATATYPE_WAVE_FILE        = 10, /**<录波文件*/
    CSG_LOG_UNIT_DATATYPE_WAVE_FILE_LIST   = 11, /**<录波文件列表*/
	CSG_LOG_UNIT_DATATYPE_WAVE_FILE_REPORT = 12, /**<录波简报*/
	CSG_LOG_UNIT_DATATYPE_COMMON_FILE      = 13, /**<通用文件*/
	CSG_LOG_UNIT_DATATYPE_COMMON_FILE_LIST = 14, /**<通用文件列表*/
	CSG_LOG_UNIT_DATATYPE_SOFT             = 15, /**<软件*/
	CSG_LOG_UNIT_DATATYPE_CFG_FILE         = 16, /**<配置文件*/
	CSG_LOG_UNIT_DATATYPE_LOG_FILE         = 17, /**<日志文件*/
	CSG_LOG_UNIT_DATATYPE_DATABASE         = 18, /**<数据库*/
	CSG_LOG_UNIT_DATATYPE_OSC_AI           = 19, /**<录波模拟量通道*/
	CSG_LOG_UNIT_DATATYPE_OSC_DI           = 20, /**<录波开关量通道*/
	CSG_LOG_UNIT_DATATYPE_IED              = 21, /**<二次设备*/
	CSG_LOG_UNIT_DATATYPE_IED_CPU          = 22, /**<CPU*/
	CSG_LOG_UNIT_DATATYPE_PRM_DEV          = 23, /**<一次设备*/
	CSG_LOG_UNIT_DATATYPE_OTHER            = 999 /**<其它*/
};

/** @brief日志操作行为结果 */
enum CSG_LOG_UNIT_BEHAVIOR_RESULT
{
	CSG_LOG_UNIT_BEHAVIOR_RESULT_SUCCEED           = 0,/**<成功*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_FAIL              = 1,/**<失败*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_SELECT_SUCCEED    = 2,/**<选择成功*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_SELECT_FAIL       = 3,/**<选择失败*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_EXEC_SUCCEED      = 4,/**<执行成功*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_EXEC_FAIL         = 5,/**<执行失败*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_COMMU_NOMAL       = 6,/**<通信正常*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_COMMU_INTERRUPT   = 7,/**<通信中断*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT_COMMU_OTHER       = 999/**<其它*/
};
/** @brief 日志记录单元*/
//[*] 要求必须填写  [/] 可选填写
typedef struct _CSG_LOG_UNIT
{
	CSG_LOG_UNIT_ATTRIBUTE  e_attribute;// [*] 记录的属性
	string                  str_sponer; // [*] 该记录内容的触发主体。对于修改定值的操作，其操作源为调度端主站，
	                                    //     对于子站根据保护设备的新录波文件信息触发的召唤录波文件操作，其操作源为子站设备
	CSG_LOG_UNIT_BEHAVIOR   e_behavior;//[*] 该记录对应的行为类型
		/*统一规范行为使用的词汇:
	    报文接收过程：解析；
		文件传输过程：读取、召唤、接收、上送；
		遥控过程：遥控、复归、投入、退出；
		定值操作过程：召唤、接收、下传、固化；
		定值区操作过程：召唤、接收、切换、固化；
		文件维护过程：删除、新建；
		总召唤过程：总召唤；
		子站维护过程：新建、删除、升级、扩建、修改*/
	string                 str_target;//[*] 该记录行为对应的实体靶标对象
		/*其范围为配置入子站的所有接入设备、所有转出主站、子站本体或者本体的部分(如对时插件、信号灯)。
		由保护设备的行为触发的日志记录，如保护设备突发上送扰动数据，其发起者为保护设备，
		而靶标对象则为信息子站，当子站根据该突发扰动启动录波召唤过程时，发起者为信息子站，而靶标对象为保护设备。
		由调度主站发起的操作过程，其发起者为调度主站，靶标对象为待操作的设备
		*/
	CSG_LOG_UNIT_DATATYPE  e_log_unit_datatype; // [/] 该记录中行为对应的最终数据对象的类型
	string                 str_dataobject;      // [/] 该记录中行为对应的最终数据对象
	     /*当数据对象在配置文件中有描述，则填入描述。
	      当数据对象在配置文件中无描述，但是在规范中有标准的命名方式时，按照标准填写，
	      例如DataType为录波文件时，DataObject可以填写该录波文件的原始文件名*/
	string                 str_dataauxiliary;//  [/]   DataObject的辅助描述，该条目可选
	     /*把数据对象其他有参考意义的信息填入该栏目，以辅助理解日志内容。
		 数据对象的索引(如61850规约的reference，103规约的GIN)可以写入该栏目以便于定位数据对象*/
	string                 str_subbehavior;// [/] 行为的子状态，用于区分一个具体行为的中间状态
	     /*，在子站执行时有时需要分解为多个子过程进行，使用该标签用于对行为的详细过程进行区分
		 规范子状态的词汇为：发起、开始、锁定、进行中、结束*/
	CSG_LOG_UNIT_BEHAVIOR_RESULT 	e_behavior_result;//[/]  行为结果，该条目可选。用于填写该记录中行为的返回结果
	string  str_attachinfo;   //[/]  附加信息，自行定义
	_CSG_LOG_UNIT()
	{
		e_attribute = CSG_LOG_UNIT_ATTRIBUTE_OTHER;
		str_sponer  = "sponer";
		e_behavior  = CSG_LOG_UNIT_BEHAVIOR_OTHER;
		str_target ="target";
		e_log_unit_datatype = CSG_LOG_UNIT_DATATYPE_OTHER;
		str_dataobject ="csg_dataobj";
		str_dataauxiliary = "csg_dataauxiliary";
		str_subbehavior = "subbehavior";
		e_behavior_result = CSG_LOG_UNIT_BEHAVIOR_RESULT_COMMU_OTHER;
		str_attachinfo = "csg_attachinfo";

	}
	
}CSG_LOG_DATA_UNIT;
/** @brief 日志类*/
class CCsgLogRecord
{
public:
	CCsgLogRecord(CSG_LOG_RECORD_CFG  csglogrecord_cfg,unsigned int nCurLogRole = 0);
	~CCsgLogRecord(void);
//////////////////////////////////////////////////////////////////////////日志操作接口
public:
	  /**
	  * @brief         写日志
	  * @param[in]     CSG_LOG_DATA_UNIT& csg_log_unit  日志配置结构
	  * @param[out]    无
	  * @return        int
	  */
	 int Csg_LogOut(CSG_LOG_DATA_UNIT& csg_log_unit);
	  /**
	  * @brief         设置配置
	  * @param[in]     CSG_LOG_RECORD_CFG& LogRecord_Cfg  日志配置结构
	  * @param[out]    无
	  * @return        无
	  */
	 void Set_Csg_Log_Record_Cfg(CSG_LOG_RECORD_CFG& LogRecord_Cfg);
private:
	/**
	* @brief         开始日志记录，使用默认对象名
	* @param[in]     无
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool __BeginLog();
	/**
	* @brief         结束日志记录
	* @param[in]     无
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool __EndLog();
private:
		/**
	* @brief         打开日志文件
	* @param[in]     无
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool __OpenLogFile();

	/**
	* @brief         关闭日志文件
	* @param[in]     无
	* @param[out]    无
	* @return        bool true:成功 false:失败
	*/
	bool __CloseLogFile();
	/**
	* @brief         按南网格式要求创建输出内容
	* @param[in]     CSG_LOG_DATA_UNIT& csg_log_unit
	* @param[out]    string& strContent
	* @return        无
	*/
	void __CreateCsgLogContent(CSG_LOG_DATA_UNIT& csg_log_unit, string& strContent);
	/**
	* @brief         写入到磁盘日志文件
	* @param[in]     strContent  数据
	* @param[out]    无
	* @return        void
	*/
	void __Flush2LogFile(string&  strContent);
private:
	/**
	* @brief         初始化枚举的单词
	* @param[in]     无
	* @param[out]    无
	* @return        void
	*/
	void __InitLogUnitEnumWords();
	/**
	* @brief         获取日志单元ID号
	* @param[in]     无
	* @param[out]    无
	* @return        unsigned int  日志ID号
	*/
	string __GetLogUnit_Id();
	/**
	* @brief         获取日志记录属性
	* @param[in]     unsigned int nId
	* @param[out]    无
	* @return        void
	*/
	string __GetLogUnitEnum_Log_Attr(unsigned int nEnumCode);
	/**
	* @brief         获取日志数据类型
	* @param[in]     unsigned int nId
	* @param[out]    无
	* @return        void
	*/
	string __GetLogUnitEnum_Log_Datatype(unsigned int nEnumCode);
		/**
	* @brief         获取日志行为
	* @param[in]     unsigned int nId
	* @param[out]    无
	* @return        void
	*/
	string __GetLogUnitEnum_Log_Behavior(unsigned int nEnumCode);
	/**
	* @brief         获取日志日志操作行为结果
	* @param[in]     unsigned int nId
	* @param[out]    无
	* @return        void
	*/
	string __GetLogUnitEnum_Log_Behavior_Result(unsigned int nEnumCode);
private:
	  /**
	  * @brief         清除指定路径下的文件
	  * @param[in]     unsigned int nLogType
	  * @param[out]    无
	  * @return        string
	  */
	void __CleanDirContent(string strPathName);
	 /**
	  * @brief         清除指定路径下的文件
	  * @param[in]     const char* filename 文件全路径名
	  * @param[in]     time_t nLastTime 文件最后更新时间
	  * @param[out]    无
	  * @return        string
	  */
	int  __DeleteFile(const char* filefullpathname,time_t nLastTime);
private:
	unsigned int m_nLogRole;//日志角色
	/** @日志配置项目 */
	CSG_LOG_RECORD_CFG  m_csglogrecord_cfg;
	////////////  路径拼凑方式 :  文件根目录/日志类型/文件名*/
	//文件名：文件类型识别_时间戳字符串_标识符.后缀
	//运行日志识别符为RUNLOG   操作日志识别符为OPRLOG  维护日志识别符为MNTLOG
	//时间戳字符串：YYYY_MM_DD_HH_mm_SS_ZZZ
	//标识符：模块编号(只能为字母和数字)
	//后缀:txt
	/** @brief  	 日志文件全路径名，含文件路径 */
	char   m_szLogRecordFullPathName[FILE_OPERATE_FILENAME_MAX_LEN];
	/** @brief  	 日志类型名称 */
	char   m_szLogTypeName[32];
	/** @brief  	 标识符 */
	char   m_szModuleCode[32];
	/** @brief  	 日志ID号，顺序递增 */
	unsigned int m_nlog_id;
	/** @brief  	 当前日志文件的大小 */
	long  m_nCurrenSize;
	long m_nfileLifeSecods;//文件存活时间 秒
private:
	/** @brief       文件处理指针*/
	CFileOperate   	*m_pLogFile;
	/** @brief      互斥体*/
	CThreadMutualLock m_mutualLock;
private:
	/** @brief      记录属性*/
	map<unsigned int,string> m_maplist_log_attr;
	/** @brief      数据对象类型*/
	map<unsigned int,string> m_maplist_log_datatype;
	/** @brief      日志行为*/
	map<unsigned int,string> m_maplist_log_behavior;
	/** @brief      日志操作行为结果*/
	map<unsigned int,string> m_maplist_log_behavior_result;
};
#endif

